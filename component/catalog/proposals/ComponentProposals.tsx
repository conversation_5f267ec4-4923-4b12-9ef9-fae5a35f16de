'use client';

import React, { useEffect, useState, useMemo } from 'react';

import { useRouter } from 'next/navigation';
import PagePagination from '@/component/pagination/ComponentPagePagination';
import { CustomerType, CustomerStatus } from '@/lib/graphql/types/generated/graphql';
import ComponentLoading from '@/component/common/ComponentLoading';
import ProposalControls from './ComponentProposalControls';
import { ProposalStatus, ProposalWithBasicDetails } from '@/types/component/proposals/TypeProposal';
import ConstantProposalTableHeaders from '@/constants/ConstantsProposalTableHeaders';

// Status color mapping for quote status badges
const statusColorMap: { [key: string]: string } = {
    'DRAFT': 'bg-gray-100 text-gray-800',
    'PENDING': 'bg-yellow-100 text-yellow-800',
    'APPROVED': 'bg-green-100 text-green-800',
    'REJECTED': 'bg-red-100 text-red-800',
    'EXPIRED': 'bg-red-100 text-red-800',
};

// Dummy data for quotes - to be replaced with GraphQL calls later
const dummyQuotesData: ProposalWithBasicDetails[] = [
    {
        id: '1',
        proposalNumber: 'QT-2024-001',
        customerName: 'Acme Corporation',
        customerType: CustomerType.Business,
        customerStatus: CustomerStatus.Active,
        salesExecutive: 'John Smith',
        salesExecutiveId: 'user-1',
        totalValue: 15750.00,
        currency: 'USD',
        status: ProposalStatus.PENDING,
        createdDate: '2024-01-15',
        expiryDate: '2024-02-15',
        lastModified: '2024-01-20',
        customerAcceptedDate: null
    },
    {
        id: '2',
        proposalNumber: 'QT-2024-002',
        customerName: 'Tech Solutions Inc',
        customerType: CustomerType.Business,
        customerStatus: CustomerStatus.Active,
        salesExecutive: 'Sarah Johnson',
        salesExecutiveId: 'user-2',
        totalValue: 28900.00,
        currency: 'USD',
        status: ProposalStatus.APPROVED,
        createdDate: '2024-01-18',
        expiryDate: '2024-02-18',
        lastModified: '2024-01-22',
        customerAcceptedDate: '2024-01-22'
    },
    {
        id: '3',
        proposalNumber: 'QT-2024-003',
        customerName: 'Global Enterprises',
        customerType: CustomerType.Business,
        customerStatus: CustomerStatus.Active,
        salesExecutive: 'Mike Davis',
        salesExecutiveId: 'user-3',
        totalValue: 45200.00,
        currency: 'USD',
        status: ProposalStatus.DRAFT,
        createdDate: '2024-01-20',
        expiryDate: '2024-02-20',
        lastModified: '2024-01-25',
        customerAcceptedDate: null
    },
    {
        id: '4',
        proposalNumber: 'QT-2024-004',
        customerName: 'Innovation Labs',
        customerType: CustomerType.Business,
        customerStatus: CustomerStatus.Active,
        salesExecutive: 'Emily Wilson',
        salesExecutiveId: 'user-4',
        totalValue: 12300.00,
        currency: 'USD',
        status: ProposalStatus.REJECTED,
        createdDate: '2024-01-12',
        expiryDate: '2024-02-12',
        lastModified: '2024-01-28',
        customerAcceptedDate: null
    },
    {
        id: '5',
        proposalNumber: 'QT-2024-005',
        customerName: 'Digital Dynamics',
        customerType: CustomerType.Business,
        customerStatus: CustomerStatus.Active,
        salesExecutive: 'Robert Brown',
        salesExecutiveId: 'user-5',
        totalValue: 67800.00,
        currency: 'USD',
        status: ProposalStatus.PENDING,
        createdDate: '2024-01-25',
        expiryDate: '2024-02-25',
        lastModified: '2024-01-30',
        customerAcceptedDate: null
    },
    {
        id: '6',
        proposalNumber: 'QT-2024-006',
        customerName: 'StartupCo',
        customerType: CustomerType.Business,
        customerStatus: CustomerStatus.Active,
        salesExecutive: 'Lisa Chen',
        salesExecutiveId: 'user-6',
        totalValue: 8500.00,
        currency: 'USD',
        status: ProposalStatus.EXPIRED,
        createdDate: '2024-01-05',
        expiryDate: '2024-01-20',
        lastModified: '2024-01-15',
        customerAcceptedDate: null
    },
    {
        id: '7',
        proposalNumber: 'QT-2024-007',
        customerName: 'Enterprise Solutions Ltd',
        customerType: CustomerType.Business,
        customerStatus: CustomerStatus.Active,
        salesExecutive: 'David Kim',
        salesExecutiveId: 'user-7',
        totalValue: 125000.00,
        currency: 'USD',
        status: ProposalStatus.APPROVED,
        createdDate: '2024-01-28',
        expiryDate: '2024-02-28',
        lastModified: '2024-02-02',
        customerAcceptedDate: '2024-02-01'
    },
    {
        id: '8',
        proposalNumber: 'QT-2024-008',
        customerName: 'Local Business Inc',
        customerType: CustomerType.Business,
        customerStatus: CustomerStatus.Active,
        salesExecutive: 'Anna Rodriguez',
        salesExecutiveId: 'user-8',
        totalValue: 3200.00,
        currency: 'USD',
        status: ProposalStatus.DRAFT,
        createdDate: '2024-02-01',
        expiryDate: '2024-03-01',
        lastModified: '2024-02-03',
        customerAcceptedDate: null
    }
];

const ComponentProposals: React.FC = () => {
    const router = useRouter();

    const [visibleHeaders, setVisibleHeaders] = useState(ConstantProposalTableHeaders);
    const [searchQuery, setSearchQuery] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [isLoading, setIsLoading] = useState(false);
    const [quotes, setQuotes] = useState<ProposalWithBasicDetails[]>([]);
    const [error, setError] = useState<string | null>(null);
    const [paginatedQuotes, setPaginatedQuotes] = useState<ProposalWithBasicDetails[]>([]);

    const recordsPerPage = 10;

    // Filter quotes based on search query
    const filteredQuotes = useMemo(() => {
        if (!searchQuery.trim()) return quotes;
        const query = searchQuery.toLowerCase().trim();
        return quotes.filter(quote =>
            Object.values(quote).some(value =>
                String(value).toLowerCase().includes(query)
            )
        );
    }, [searchQuery, quotes]);

    const totalPages = Math.ceil(filteredQuotes.length / recordsPerPage);

    useEffect(() => {
        const paginatedQuotes = filteredQuotes.slice(
            (currentPage - 1) * recordsPerPage,
            currentPage * recordsPerPage
        );
        setPaginatedQuotes(paginatedQuotes);
    }, [filteredQuotes, currentPage]);

    // Initialize with dummy data - will be replaced with GraphQL query
    useEffect(() => {
        setCurrentPage(1);
        setIsLoading(true);

        // Simulate loading delay
        setTimeout(() => {
            setQuotes(dummyQuotesData);
            setIsLoading(false);
        }, 500);
    }, []);

    const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchQuery(e.target.value);
        setCurrentPage(1); // Reset to first page when searching
    };

    const handleRowClick = (quoteId: string) => {
        router.push(`/catalog/proposals/new?mode=edit&id=${quoteId}`);
    };

    const handleAddProposal = () => {
        router.push('/catalog/proposals/new');
    };

    // Format currency value
    const formatCurrency = (value: number, currency: string) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency,
        }).format(value);
    };

    // Format date
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    return (
        <div className="space-y-6">
            {/* Header Section */}
            <div className="flex items-center justify-between">
                <div className="flex flex-col gap-1">
                    <h2 className="text-2xl font-bold text-gray-900">Proposals</h2>
                    <p className="text-sm text-gray-600">
                        Manage your sales quotes and proposals
                    </p>
                </div>
                <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <div className="text-center">
                        <div className="text-lg font-semibold text-gray-900">{quotes.length}</div>
                        <div>Total Quotes</div>
                    </div>
                    <div className="text-center">
                        <div className="text-lg font-semibold text-green-600">
                            {quotes.filter(q => q.status === ProposalStatus.APPROVED).length}
                        </div>
                        <div>Approved</div>
                    </div>
                    <div className="text-center">
                        <div className="text-lg font-semibold text-yellow-600">
                            {quotes.filter(q => q.status === ProposalStatus.PENDING).length}
                        </div>
                        <div>Pending</div>
                    </div>
                </div>
            </div>

            <ProposalControls
                searchQuery={searchQuery}
                onSearch={handleSearch}
                onAddProposal={handleAddProposal}
            />

            {isLoading && (
                <div className="bg-white rounded-lg border border-gray-200">
                    <ComponentLoading
                        message="Loading quotes..."
                        className="min-h-[400px]"
                    />
                </div>
            )}

            {!isLoading && error && (
                <div className="bg-white rounded-lg border border-gray-200 p-4">
                    <div className="text-red-600 text-center">
                        {error}
                    </div>
                </div>
            )}

            {!isLoading && !error && (
                <div className="flex flex-col">
                    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
                        <div className="overflow-x-auto">
                            <div className="min-w-full">
                                <div className="flex bg-gray-50 border-b border-gray-200">
                                    {visibleHeaders.map((header) => (
                                        <div key={header.label} className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider flex-1 min-w-0">
                                            {header.label}
                                        </div>
                                    ))}
                                </div>
                                <div>
                                    {paginatedQuotes.length === 0 ? (
                                        <div className="px-4 py-12 text-center text-gray-500">
                                            <div className="text-lg font-medium mb-2">No quotes found</div>
                                            <div className="text-sm">
                                                {searchQuery ? 'Try adjusting your search criteria' : 'Create your first quote to get started'}
                                            </div>
                                        </div>
                                    ) : (
                                        paginatedQuotes.map((quote) => (
                                            <div
                                                key={quote.id}
                                                className="flex border-b border-gray-200 hover:bg-gray-50 cursor-pointer transition-all duration-200 hover:shadow-sm"
                                                onClick={() => handleRowClick(quote.id)}
                                                role="button"
                                                tabIndex={0}
                                                onKeyDown={(e) => {
                                                    if (e.key === 'Enter' || e.key === ' ') {
                                                        e.preventDefault();
                                                        handleRowClick(quote.id);
                                                    }
                                                }}
                                            >
                                                {visibleHeaders.map((header) => {
                                                    let cellValue: any = header.key ? quote[header.key as keyof ProposalWithBasicDetails] : '';

                                                    // Special formatting for specific fields
                                                    if (header.key === 'totalValue') {
                                                        cellValue = formatCurrency(quote.totalValue, quote.currency);
                                                    } else if (header.key === 'createdDate') {
                                                        cellValue = formatDate(quote.createdDate);
                                                    } else if (header.key === 'customerAcceptedDate') {
                                                        cellValue = quote.customerAcceptedDate ? formatDate(quote.customerAcceptedDate) : 'Not accepted';
                                                    }

                                                    return (
                                                        <div key={header.label} className="my-4 mx-4 overflow-hidden text-ellipsis text-sm text-gray-700 flex-1 min-w-0">
                                                            {header.pill ? (
                                                                <span className={`px-3 py-1 rounded-full text-xs font-semibold overflow-hidden text-ellipsis whitespace-nowrap ${statusColorMap[cellValue as string] || 'bg-gray-100 text-gray-800'}`}>
                                                                    {cellValue}
                                                                </span>
                                                            ) : (
                                                                <span
                                                                    className={`overflow-hidden text-ellipsis whitespace-nowrap ${header.key === 'proposalNumber' || header.key === 'customerName' ? 'font-medium' : ''}`}
                                                                    title={String(cellValue)}
                                                                >
                                                                    {cellValue}
                                                                </span>
                                                            )}
                                                        </div>
                                                    );
                                                })}
                                            </div>
                                        ))
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>

                    {totalPages > 1 && (
                        <div className="flex justify-end items-center p-4">
                            <PagePagination
                                currentPage={currentPage}
                                totalPages={totalPages}
                                onPageChange={setCurrentPage}
                            />
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default ComponentProposals;