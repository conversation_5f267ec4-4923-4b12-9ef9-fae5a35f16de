'use client';

import React, { useState, useEffect } from 'react';
import { X, Search, Package, Plus, Check } from 'lucide-react';
import { ProposalProduct, MasterProductForProposal, ProductSelectionModalProps } from '@/types/component/proposals/TypeProposal';
import { Currency, ProductStatus } from '@/lib/graphql/types/generated/graphql';
import { useGetAllProductsQuery } from '@/lib/graphql/types/generated/hooks';
import { Input } from '@headlessui/react';
import ComponentLoading from '@/component/common/ComponentLoading';
import ComponentNote from '@/component/common/ComponentNote';

const ComponentProductSelectionModal: React.FC<ProductSelectionModalProps> = ({
    isOpen,
    onClose,
    onProductSelect,
    selectedProducts
}) => {
    // Fetch products using Apollo query
    const { data: productsData, loading: productsLoading, error: productsError } = useGetAllProductsQuery({
        variables: {
            status: ProductStatus.Active, // Only fetch active products
        }
    });
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedProduct, setSelectedProduct] = useState<MasterProductForProposal | null>(null);
    const [quantity, setQuantity] = useState(1);
    const [discount, setDiscount] = useState(0);
    const [tax, setTax] = useState(0);
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [isNewProduct, setIsNewProduct] = useState(false);
    const dropdownRef = React.useRef<HTMLDivElement>(null);

    // Transform GraphQL product data to MasterProductForProposal format
    const masterProducts: MasterProductForProposal[] = productsData?.masterProductsGet.map(product => ({
        id: product.id,
        name: product.name,
        productCode: product.productCode || '', // Handle null productCode
        description: product.description || undefined,
        dimensions: product.dimensions?.map(dim => ({
            key: dim.key,
            value: dim.value
        })) || [],
        listPrice: {
            value: product.pricing.listPrice.value,
            currency: product.pricing.listPrice.currency
        },
        status: product.status
    })) || [];

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsDropdownOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const filteredProducts = masterProducts.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.dimensions.some(dim =>
            dim.key.toLowerCase().includes(searchQuery.toLowerCase()) ||
            dim.value.toLowerCase().includes(searchQuery.toLowerCase())
        )
    );

    const isProductAlreadySelected = (productId: string) => {
        return selectedProducts.some(p => p.id === productId);
    };

    const handleProductSelect = (product: MasterProductForProposal) => {
        setSelectedProduct(product);
        setQuantity(1);
        setDiscount(0);
        setIsNewProduct(false);
    };

    const handleConfirm = () => {
        if (!selectedProduct) return;

        const discountAmount = (selectedProduct.listPrice.value * discount) / 100;
        const sellingPrice = selectedProduct.listPrice.value - discountAmount;
        const taxAmount = (sellingPrice * tax) / 100;

        const proposalProduct: ProposalProduct = {
            id: selectedProduct.id,
            name: selectedProduct.name,
            dimensions: selectedProduct.dimensions,
            listPrice: selectedProduct.listPrice,
            discount: discount > 0 ? {
                percentage: discount,
                value: discountAmount,
                currency: selectedProduct.listPrice.currency
            } : undefined,
            sellingPrice: {
                value: sellingPrice,
                currency: selectedProduct.listPrice.currency
            },
            tax: tax > 0 ? {
                percentage: tax,
                value: taxAmount,
                currency: selectedProduct.listPrice.currency
            } : undefined,
            quantity
        };

        onProductSelect(proposalProduct);
        handleClose();
    };

    const handleClose = () => {
        setSelectedProduct(null);
        setQuantity(1);
        setDiscount(0); //todo: should come from the product itself
        setSearchQuery('');
        onClose();
    };

    const formatCurrency = (value: number, currency: string) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency,
        }).format(value);
    };

    // Handle escape key
    useEffect(() => {
        const handleEscape = (e: KeyboardEvent) => {
            if (e.key === 'Escape') {
                handleClose();
            }
        };

        if (isOpen) {
            document.addEventListener('keydown', handleEscape);
            document.body.style.overflow = 'hidden';
        }

        return () => {
            document.removeEventListener('keydown', handleEscape);
            document.body.style.overflow = 'unset';
        };
    }, [isOpen]);

    if (!isOpen) return null;

    // Handle loading state
    if (productsLoading) {
        return (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                    <h2 className="text-xl font-bold text-gray-900 mb-4">Add Proposal Product</h2>
                    <ComponentLoading message="Loading products..." className="min-h-[200px]" />
                </div>
            </div>
        );
    }

    // Handle error state
    if (productsError) {
        return (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                    <h2 className="text-xl font-bold text-gray-900 mb-4">Add Proposal Product</h2>
                    <ComponentNote isError={true}>
                        Error loading products: {productsError.message}
                    </ComponentNote>
                    <div className="mt-4 flex justify-end">
                        <button
                            onClick={onClose}
                            className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
                        >
                            Close
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    const handleAddNewProduct = () => {
        setIsNewProduct(true);
        setSelectedProduct({
            id: '',
            name: '',
            dimensions: [],
            listPrice: { value: 0, currency: Currency.Usd },
            status: 'ACTIVE'
        } as MasterProductForProposal);
        setSearchQuery('');
    };

    const showSelectedProduct = () => {
        if (!selectedProduct) return null;
        return (
            <div className="space-y-4">
                <div className="p-4 bg-gray-50 rounded-lg">
                    {isNewProduct ? (
                        <div className='flex flex-row items-center gap-2'>
                            <label className="font-medium text-gray-900 w-30">Product Name: </label>
                            <Input
                                className="border-b border-gray-300 font-medium focus:border-primary focus:ring-0"
                                value={selectedProduct.name}
                                placeholder='My New Product'
                                onChange={(e) => setSelectedProduct({ ...selectedProduct, name: e.target.value })}
                            />
                        </div>
                    ) : (
                        <h4 className="font-medium text-gray-900 mb-2">{selectedProduct.name}</h4>
                    )}
                    <div className="flex flex-wrap gap-2 mb-2">
                        {selectedProduct.dimensions.map((dim, index) => (
                            <span
                                key={index}
                                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                            >
                                {dim.key}: {dim.value}
                            </span>
                        ))}
                    </div>
                    <div>
                        {isNewProduct ? (
                            <div className='flex flex-row items-center gap-2'>
                                <label className="font-medium text-gray-900 w-30">List Price: </label>
                                <Input
                                    type='number'
                                    className="border-b border-gray-300 font-medium focus:border-primary focus:ring-0"
                                    value={selectedProduct.listPrice.value}
                                    placeholder='List Price'
                                    onChange={(e) => setSelectedProduct({ ...selectedProduct, listPrice: { ...selectedProduct.listPrice, value: parseFloat(e.target.value) || 0 } })}
                                />
                            </div>
                        ) : (
                            <p className="text-sm text-gray-600">
                                List Price: {formatCurrency(selectedProduct.listPrice.value, selectedProduct.listPrice.currency)}
                            </p>
                        )}

                    </div>
                </div>

                <div className="grid grid-cols-3 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Quantity
                        </label>
                        <input
                            type="number"
                            min="1"
                            value={quantity}
                            onChange={(e) => setQuantity(parseInt(e.target.value) || 1)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Discount (%)
                        </label>
                        <input
                            type="number"
                            min="0"
                            max="100"
                            value={discount}
                            onChange={(e) => setDiscount(parseFloat(e.target.value) || 0)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Tax (%)
                        </label>
                        <input
                            type="number"
                            min="0"
                            max="100"
                            value={tax}
                            onChange={(e) => setTax(parseFloat(e.target.value) || 0)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        />
                    </div>
                </div>

                {/* Price Summary */}
                <div className="p-4 bg-gray-50 rounded-lg">
                    <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                            <span>List Price:</span>
                            <span>{formatCurrency(selectedProduct.listPrice.value, selectedProduct.listPrice.currency)}</span>
                        </div>
                        {(
                            <div className="flex justify-between text-green-600">
                                <span>Discount ({discount}%):</span>
                                <span>-{formatCurrency((selectedProduct.listPrice.value * discount) / 100, selectedProduct.listPrice.currency)}</span>
                            </div>
                        )}
                        <div className="flex justify-between font-medium border-t pt-2">
                            <span>Selling Price:</span>
                            <span>{formatCurrency(selectedProduct.listPrice.value - (selectedProduct.listPrice.value * discount) / 100, selectedProduct.listPrice.currency)}</span>
                        </div>
                        {(
                            <div className="flex justify-between">
                                <span>Tax ({tax}%):</span>
                                <span>
                                    {
                                        formatCurrency(
                                            (
                                                (selectedProduct.listPrice.value - (selectedProduct.listPrice.value * discount) / 100) * tax / 100
                                            ),
                                            selectedProduct.listPrice.currency)
                                    }
                                </span>
                            </div>
                        )}
                        <div className="flex justify-between font-bold text-primary border-t pt-2">
                            <span>Total:</span>
                            <span>{
                                formatCurrency(
                                    ((selectedProduct.listPrice.value - (selectedProduct.listPrice.value * discount) / 100) * quantity)
                                    + ((selectedProduct.listPrice.value - (selectedProduct.listPrice.value * discount) / 100) * tax / 100),
                                    selectedProduct.listPrice.currency
                                )
                            }</span>
                        </div>
                    </div>
                </div>
            </div>

        )
    };

    return (
        <div className="fixed inset-0 z-50 backdrop-blur-md shadow-md overflow-y-auto border border-gray-200">
            {/* Backdrop */}
            <div
                className="fixed inset-0 bg-opacity-90 transition-opacity"
                onClick={handleClose}
            />

            {/* Modal */}
            <div className="flex min-h-full items-center justify-center p-4">
                <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
                    {/* Header */}
                    <div className="flex items-center border justify-between p-6 pb-4  border-gray-200">
                        <div>
                            <h2 className="text-xl font-bold text-gray-900">Add Proposal Product</h2>
                            <p className="text-sm text-gray-600 mt-1">
                                You can select a product from your catalog or add a new one
                            </p>
                        </div>
                        <button
                            onClick={handleClose}
                            className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                        >
                            <X className="h-6 w-6" />
                        </button>
                    </div>

                    {/* Content */}
                    <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
                        <div className="flex flex-col gap-6">
                            {/* Product Selection */}
                            <div>
                                <div className="flex items-center gap-3">
                                    {/* Searchable Dropdown */}
                                    <div className="relative w-full" ref={dropdownRef}>
                                        <div className="relative">
                                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                                            <input
                                                type="text"
                                                placeholder="Search and select a product..."
                                                value={searchQuery}
                                                onChange={(e) => {
                                                    setSearchQuery(e.target.value);
                                                    setIsDropdownOpen(true);
                                                }}
                                                onFocus={() => setIsDropdownOpen(true)}
                                                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                            />
                                        </div>

                                        {/* Dropdown List */}
                                        {isDropdownOpen && (searchQuery.length > 0 || filteredProducts.length > 0) && (
                                            <div className="absolute z-10 w-full mt-2 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto divide-y divide-gray-100">
                                                {filteredProducts.map((product) => (
                                                    <div
                                                        key={product.id}
                                                        onClick={() => {
                                                            if (!isProductAlreadySelected(product.id)) {
                                                                handleProductSelect(product);
                                                                setIsDropdownOpen(false);
                                                            }
                                                        }}
                                                        className={`p-3 cursor-pointer transition-colors hover:bg-gray-50 ${selectedProduct?.id === product.id ? 'bg-blue-50' : ''
                                                            } ${isProductAlreadySelected(product.id) ? 'opacity-50 cursor-not-allowed' : ''
                                                            }`}
                                                    >
                                                        <div className="flex flex-col">
                                                            <div className="flex items-center justify-between">
                                                                <div className="flex items-center gap-2 flex-wrap">
                                                                    <h4 className="font-medium text-gray-900">{product.name}</h4>
                                                                    {product.dimensions.map((dim, index) => (
                                                                        <span
                                                                            key={index}
                                                                            className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                                                                        >
                                                                            {dim.key}: {dim.value}
                                                                        </span>
                                                                    ))}
                                                                </div>
                                                                {isProductAlreadySelected(product.id) && (
                                                                    <Check className="h-5 w-5 text-green-600 ml-2 flex-shrink-0" />
                                                                )}
                                                            </div>
                                                            <p className="text-sm text-gray-600 mt-1.5">
                                                                {formatCurrency(product.listPrice.value, product.listPrice.currency)}
                                                            </p>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        )}
                                    </div>
                                    <button
                                        type="button"
                                        onClick={handleAddNewProduct}
                                        className="bg-secondary text-white w-[15vh] px-3 py-2 rounded-md hover:bg-opacity-90 transition-colors"
                                    >
                                        <span className='flex items-center' >
                                            <Plus className="h-4 w-4" />
                                            New Product
                                        </span>
                                    </button>
                                </div>

                            </div>

                            {/* Product Configuration */}
                            <div>
                                <h3 className="text-lg font-medium text-gray-900 mb-4">Configuration</h3>
                                {selectedProduct
                                    ? showSelectedProduct()
                                    : (
                                        <div className="text-center py-8 text-gray-500">
                                            <Package className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                                            <p>Select a product to configure</p>
                                        </div>
                                    )}
                            </div>
                        </div>
                    </div>

                    {/* Footer */}
                    <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200">
                        <button
                            onClick={handleClose}
                            className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                        >
                            Cancel
                        </button>
                        <button
                            onClick={handleConfirm}
                            disabled={!selectedProduct}
                            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-opacity-90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                        >
                            <Check className="h-4 w-4" />
                            Confirm Product
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ComponentProductSelectionModal;
