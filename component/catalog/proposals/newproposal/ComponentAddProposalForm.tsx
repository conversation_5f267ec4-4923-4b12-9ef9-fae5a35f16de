'use client';

import React, { useState, useEffect } from 'react';
import { ArrowLeft, Save, Send, Plus } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useForm, FormProvider } from 'react-hook-form';
import ComponentLoading from '@/component/common/ComponentLoading';
import ComponentNote from '@/component/common/ComponentNote';
import { ProposalFormData, BillingAddress, QuoteDetails, AccountAssignment, ProposalProduct } from '@/types/component/proposals/TypeProposal';
import { Currency, QuoteStatus } from '@/lib/graphql/types/generated/graphql';
import ComponentProposalBasicDetails from './ComponentProposalBasicDetails';
import ComponentProposalProducts from './ComponentProposalProductDetails';
import ComponentProductSelectionModal from './ComponentProductSelectionModal';



const ComponentAddProposalForm: React.FC = () => {
    const router = useRouter();
    const searchParams = useSearchParams();
    const mode = searchParams.get('mode') || 'new';
    const proposalId = searchParams.get('id');

    const [isLoading, setIsLoading] = useState(false);
    const [isEditMode, setIsEditMode] = useState(mode === 'edit');
    const [formNote, setFormNote] = useState<React.ReactNode | null>(null);
    const [isProductModalOpen, setIsProductModalOpen] = useState(false);

    const methods = useForm<ProposalFormData>({
        defaultValues: {
            customer: null,
            companyBillingAddress: {
                street: '',
                city: '',
                state: '',
                zipCode: '',
                country: ''
            },
            customerBillingAddress: {
                street: '',
                city: '',
                state: '',
                zipCode: '',
                country: ''
            },
            quoteDetails: {
                description: '',
                dateOfQuoteSending: '',
                expiryDate: ''
            },
            accountAssignment: {
                salesExecutive: '',
                customerSupportManager: ''
            },
            products: [],
            subtotal: {
                value: 0,
                currency: Currency.Usd
            },
            totalDiscount: {
                value: 0,
                currency: Currency.Usd
            },
            total: {
                value: 0,
                currency: Currency.Usd
            },
            status: QuoteStatus.Created,
            createdDate: new Date().toISOString(),
            lastModified: new Date().toISOString()
        }
    });

    const { watch, setValue, getValues } = methods;
    const watchedProducts = watch('products');

    // Calculate totals when products change
    useEffect(() => {
        const products = getValues('products');
        const subtotal = products.reduce((sum, product) => sum + (product.listPrice.value * product.quantity), 0);
        const totalDiscount = products.reduce((sum, product) => sum + ((product.discount?.value || 0) * product.quantity), 0);
        const totalTax = products.reduce((sum, product) => sum + (product.tax?.value || 0), 0);
        const total = subtotal - totalDiscount;

        setValue('subtotal', { value: subtotal, currency: Currency.Usd });
        setValue('totalDiscount', { value: totalDiscount, currency: Currency.Usd });
        setValue('total', { value: total, currency: Currency.Usd });
        setValue('totalTax', { value: totalTax, currency: Currency.Usd });
    }, [watchedProducts, setValue, getValues]);

    useEffect(() => {
        if (mode === 'edit' && proposalId) {
            setIsEditMode(true);
            setIsLoading(true);
            // TODO: Load proposal data for editing
            setTimeout(() => {
                setIsLoading(false);
            }, 1000);
        }
    }, [mode, proposalId]);


    const handleProductAdd = (product: ProposalProduct) => {
        const currentProducts = getValues('products');
        setValue('products', [...currentProducts, product]);
        setIsProductModalOpen(false);
    };

    const handleProductRemove = (productId: string) => {
        const currentProducts = getValues('products');
        setValue('products', currentProducts.filter(p => p.id !== productId));
    };

    const handleProductUpdate = (productId: string, updatedProduct: Partial<ProposalProduct>) => {
        const currentProducts = getValues('products');
        setValue('products', currentProducts.map(p =>
            p.id === productId ? { ...p, ...updatedProduct } : p
        ));
    };

    if (isLoading) {
        return (
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <ComponentLoading
                    message={isEditMode ? "Loading proposal..." : "Preparing form..."}
                    className="min-h-[400px]"
                />
            </div>
        );
    }


    const handleGenerate = () => {
        // based on the status of the quote, we will either send or generate
        // TODO: Implement send functionality



        setFormNote(<ComponentNote>Proposal sent successfully!</ComponentNote>);
    };

    return (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="mb-8">
                <div className="mb-8">
                    <button
                        onClick={() => router.replace('/catalog/proposals')}
                        className="mb-6 flex items-center text-gray-600 hover:text-secondary transition-colors"
                    >
                        <ArrowLeft className="h-5 w-5 mr-2" />
                        Back
                    </button>
                </div>
            </div>

            {formNote && (
                <div className="mb-6">
                    {formNote}
                </div>
            )}

            <FormProvider {...methods}>
                <form className="space-y-8" onSubmit={methods.handleSubmit(handleGenerate)}>
                    {/* Section 1 */}
                    <ComponentProposalBasicDetails />

                    {/* Section 2 */}
                    <ComponentProposalProducts
                        onAddProduct={() => setIsProductModalOpen(true)}
                        onRemoveProduct={handleProductRemove}
                        onUpdateProduct={handleProductUpdate}
                    />
                </form>
                <div className="flex items-center justify-end space-x-3">
                    <button
                        type='submit'
                        className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-opacity-90 transition-colors flex items-center gap-2"
                    >
                        <Send className="h-4 w-4" />
                        Send Proposal
                    </button>
                </div>
            </FormProvider>

            {/* Product Selection Modal */}
            <ComponentProductSelectionModal
                isOpen={isProductModalOpen}
                onClose={() => setIsProductModalOpen(false)}
                onProductSelect={handleProductAdd}
                selectedProducts={getValues('products')}
            />
        </div>
    );
};

export default ComponentAddProposalForm;
