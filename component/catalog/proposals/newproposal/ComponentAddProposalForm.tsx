'use client';

import React, { useState, useEffect } from 'react';
import { ArrowLeft, Save, Send, Plus } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useForm, FormProvider } from 'react-hook-form';
import ComponentLoading from '@/component/common/ComponentLoading';
import ComponentNote from '@/component/common/ComponentNote';
import { ProposalFormData, BillingAddress, QuoteDetails, AccountAssignment, ProposalProduct } from '@/types/component/proposals/TypeProposal';
import { Currency, QuoteStatus, QuoteUpsertInput, QuoteProductCreateInput, ProductType, ChargePolicy, DiscountType, DiscountLevel } from '@/lib/graphql/types/generated/graphql';
import { useProposalProductUpsertMutation } from '@/lib/graphql/types/generated/hooks';
import { getUserFriendlyErrorMessage, categorizeError } from '@/lib/graphql/utils/errorHandling';
import { ApolloError } from '@apollo/client';
import ComponentProposalBasicDetails from './ComponentProposalBasicDetails';
import ComponentProposalProducts from './ComponentProposalProductDetails';
import ComponentProductSelectionModal from './ComponentProductSelectionModal';



const ComponentAddProposalForm: React.FC = () => {
    const router = useRouter();
    const searchParams = useSearchParams();
    const mode = searchParams.get('mode') || 'new';
    const proposalId = searchParams.get('id');

    const [isLoading, setIsLoading] = useState(false);
    const [isEditMode, setIsEditMode] = useState(mode === 'edit');
    const [formNote, setFormNote] = useState<React.ReactNode | null>(null);
    const [isProductModalOpen, setIsProductModalOpen] = useState(false);

    // Apollo mutation hook for proposal creation
    const [proposalProductUpsert] = useProposalProductUpsertMutation();

    const methods = useForm<ProposalFormData>({
        defaultValues: {
            customer: null,
            companyBillingAddress: {
                street: '',
                city: '',
                state: '',
                zipCode: '',
                country: ''
            },
            customerBillingAddress: {
                street: '',
                city: '',
                state: '',
                zipCode: '',
                country: ''
            },
            quoteDetails: {
                description: '',
                dateOfQuoteSending: '',
                expiryDate: ''
            },
            accountAssignment: {
                salesExecutive: '',
                customerSupportManager: ''
            },
            products: [],
            subtotal: {
                value: 0,
                currency: Currency.Usd
            },
            totalDiscount: {
                value: 0,
                currency: Currency.Usd
            },
            total: {
                value: 0,
                currency: Currency.Usd
            },
            status: QuoteStatus.Created,
            createdDate: new Date().toISOString(),
            lastModified: new Date().toISOString()
        }
    });

    const { watch, setValue, getValues } = methods;
    const watchedProducts = watch('products');

    // Calculate totals when products change
    useEffect(() => {
        const products = getValues('products');
        const subtotal = products.reduce((sum, product) => sum + (product.listPrice.value * product.quantity), 0);
        const totalDiscount = products.reduce((sum, product) => sum + ((product.discount?.value || 0) * product.quantity), 0);
        const totalTax = products.reduce((sum, product) => sum + (product.tax?.value || 0), 0);
        const total = subtotal - totalDiscount;

        setValue('subtotal', { value: subtotal, currency: Currency.Usd });
        setValue('totalDiscount', { value: totalDiscount, currency: Currency.Usd });
        setValue('total', { value: total, currency: Currency.Usd });
        setValue('totalTax', { value: totalTax, currency: Currency.Usd });
    }, [watchedProducts, setValue, getValues]);

    useEffect(() => {
        if (mode === 'edit' && proposalId) {
            setIsEditMode(true);
            setIsLoading(true);
            // TODO: Load proposal data for editing
            setTimeout(() => {
                setIsLoading(false);
            }, 1000);
        }
    }, [mode, proposalId]);


    const handleProductAdd = (product: ProposalProduct) => {
        const currentProducts = getValues('products');
        setValue('products', [...currentProducts, product]);
        setIsProductModalOpen(false);
    };

    const handleProductRemove = (productId: string) => {
        const currentProducts = getValues('products');
        setValue('products', currentProducts.filter(p => p.id !== productId));
    };

    const handleProductUpdate = (productId: string, updatedProduct: Partial<ProposalProduct>) => {
        const currentProducts = getValues('products');
        setValue('products', currentProducts.map(p =>
            p.id === productId ? { ...p, ...updatedProduct } : p
        ));
    };

    if (isLoading) {
        return (
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <ComponentLoading
                    message={isEditMode ? "Loading proposal..." : "Preparing form..."}
                    className="min-h-[400px]"
                />
            </div>
        );
    }


    // Transform ProposalFormData to QuoteUpsertInput format
    const transformProposalDataToQuoteInput = (data: ProposalFormData): QuoteUpsertInput => {
        if (!data.customer) {
            throw new Error('Customer is required to create a proposal');
        }

        // Transform products to QuoteProductCreateInput format
        const quoteProductsInput: QuoteProductCreateInput[] = data.products.map(product => ({
            productCode: product.productCode,
            name: product.name,
            customerId: data.customer!.id,
            description: product.description,
            dimensions: product.dimensions.map(dim => ({
                key: dim.key,
                value: dim.value
            })),
            pricing: {
                chargePolicy: ChargePolicy.Unit, // Use the available enum value
                costPrice: {
                    value: product.listPrice.value * 0.7, // TODO: Get actual cost price
                    currency: product.listPrice.currency
                },
                listPrice: {
                    value: product.listPrice.value,
                    currency: product.listPrice.currency
                },
                sellingPrice: {
                    value: product.sellingPrice.value,
                    currency: product.sellingPrice.currency
                },
                productUnit: {
                    unit: product.quantity,
                    unitType: 'PIECE' // TODO: Get from product data when available
                },
                discounts: product.discount ? [{
                    discountType: DiscountType.Percentage,
                    discountLevel: DiscountLevel.Product,
                    discountValue: {
                        amount: {
                            value: product.discount.value,
                            currency: product.discount.currency
                        },
                        percentage: product.discount.percentage
                    }
                }] : []
            },
            // Add reference to master product if available
            referenceProduct: product.referenceProduct ? {
                type: product.referenceProduct.type,
                id: product.referenceProduct.id
            } : {
                type: ProductType.Master, // Default to master product type
                id: product.id
            },
            tax: product.tax ? [{
                name: 'Tax',
                percentage: product.tax.percentage,
                amount: {
                    value: product.tax.value,
                    currency: product.tax.currency
                }
            }] : [],
            documents: [], // TODO: Add document support when available
            customTags: [] // TODO: Add custom tags support when available
        }));

        return {
            id: data.proposalId, // undefined for new proposals
            description: data.quoteDetails.description,
            customerId: data.customer.id,
            assignedCompanyUser: data.accountAssignment.salesExecutive, // TODO: Backend needs proper assignment field
            quoteProductsInput,
            createdDate: new Date().toISOString(), // Use current timestamp
            paymentTerms: undefined, // TODO: Backend needs to add payment terms field
            discounts: [], // TODO: Add global discounts support when available
            documents: [], // TODO: Add document support when available
            customTags: [] // TODO: Add custom tags support when available
            // TODO: Backend needs to add these fields:
            // - expiryDate: data.quoteDetails.expiryDate
            // - billingAddress: data.customerBillingAddress
            // - companyBillingAddress: data.companyBillingAddress
        };
    };

    const handleGenerate = async (data: ProposalFormData) => {
        setIsLoading(true);
        setFormNote(null);

        try {
            // Validate required fields
            if (!data.customer) {
                setFormNote(<ComponentNote isError={true}>Please select a customer before creating the proposal.</ComponentNote>);
                setIsLoading(false);
                return;
            }

            if (data.products.length === 0) {
                setFormNote(<ComponentNote isError={true}>Please add at least one product to the proposal.</ComponentNote>);
                setIsLoading(false);
                return;
            }

            // Validate that all products have required fields
            const invalidProducts = data.products.filter(product =>
                !product.productCode || !product.name || product.quantity <= 0
            );

            if (invalidProducts.length > 0) {
                setFormNote(<ComponentNote isError={true}>
                    Some products are missing required information (product code, name, or valid quantity).
                    Please check all products before creating the proposal.
                </ComponentNote>);
                setIsLoading(false);
                return;
            }

            // Validate quote details
            if (!data.quoteDetails.dateOfQuoteSending) {
                setFormNote(<ComponentNote isError={true}>Please set the proposal date before creating the proposal.</ComponentNote>);
                setIsLoading(false);
                return;
            }

            // Transform data and call mutation
            const quoteInput = transformProposalDataToQuoteInput(data);

            await proposalProductUpsert({
                variables: {
                    input: quoteInput
                },
                onCompleted: (mutationData) => {
                    if (mutationData.quoteUpsert) {
                        setIsLoading(false);

                        // Update form with the new proposal ID for future edits
                        methods.setValue('proposalId', mutationData.quoteUpsert.id);

                        // Show success message with proposal details
                        const customerName = data.customer?.name || 'Unknown Customer';
                        const productCount = data.products.length;
                        const totalValue = data.total.value;

                        setFormNote(
                            <ComponentNote>
                                <div className="space-y-2">
                                    <div className="font-semibold">Proposal created successfully!</div>
                                    <div className="text-sm text-gray-600">
                                        <div>Proposal ID: {mutationData.quoteUpsert.id}</div>
                                        <div>Customer: {customerName}</div>
                                        <div>Products: {productCount}</div>
                                        <div>Total Value: ${totalValue.toFixed(2)}</div>
                                        <div>Status: {mutationData.quoteUpsert.status}</div>
                                    </div>
                                    <div className="text-sm text-gray-500 mt-2">
                                        Redirecting to proposals list in 3 seconds...
                                    </div>
                                </div>
                            </ComponentNote>
                        );

                        // Redirect to proposals list after showing success message
                        setTimeout(() => {
                            router.push('/catalog/proposals');
                        }, 3000);
                    }
                },
                onError: (error) => {
                    setIsLoading(false);
                    const errorCategory = categorizeError(error as ApolloError);

                    // Provide different error messages based on error type
                    let errorMessage = getUserFriendlyErrorMessage(error as ApolloError);

                    if (errorCategory.retryable) {
                        errorMessage += ' You can try again.';
                    }

                    if (errorCategory.type === 'validation') {
                        errorMessage = 'Please check your proposal details and try again. ' + errorCategory.technicalMessage;
                    }

                    setFormNote(<ComponentNote isError={true}>{errorMessage}</ComponentNote>);
                }
            });
        } catch (error) {
            setIsLoading(false);
            setFormNote(<ComponentNote isError={true}>
                {error instanceof Error ? error.message : 'An unexpected error occurred while creating the proposal.'}
            </ComponentNote>);
        }
    };

    return (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative">
            {/* Loading overlay */}
            {isLoading && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                        <ComponentLoading
                            message="Creating your proposal..."
                            className="min-h-[150px]"
                        />
                        <div className="text-center text-sm text-gray-600 mt-4">
                            Please wait while we process your proposal details...
                        </div>
                    </div>
                </div>
            )}

            <div className="mb-8">
                <div className="mb-8">
                    <button
                        onClick={() => router.replace('/catalog/proposals')}
                        className="mb-6 flex items-center text-gray-600 hover:text-secondary transition-colors"
                        disabled={isLoading}
                    >
                        <ArrowLeft className="h-5 w-5 mr-2" />
                        Back
                    </button>
                </div>
            </div>

            {formNote && (
                <div className="mb-6">
                    {formNote}
                </div>
            )}

            <FormProvider {...methods}>
                <form className="space-y-8" onSubmit={methods.handleSubmit(handleGenerate)}>
                    {/* Section 1 */}
                    <ComponentProposalBasicDetails />

                    {/* Section 2 */}
                    <ComponentProposalProducts
                        onAddProduct={() => setIsProductModalOpen(true)}
                        onRemoveProduct={handleProductRemove}
                        onUpdateProduct={handleProductUpdate}
                    />
                    <div className="flex items-center justify-end space-x-3">
                        <button
                            type='submit'
                            disabled={isLoading}
                            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-opacity-90 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            <Send className="h-4 w-4" />
                            {isLoading ? 'Creating Proposal...' : 'Create Proposal'}
                        </button>
                    </div>
                </form>
            </FormProvider>

            {/* Product Selection Modal */}
            <ComponentProductSelectionModal
                isOpen={isProductModalOpen}
                onClose={() => setIsProductModalOpen(false)}
                onProductSelect={handleProductAdd}
                selectedProducts={getValues('products')}
            />
        </div>
    );
};

export default ComponentAddProposalForm;
