'use client';

import React from 'react';
import { useFormContext } from 'react-hook-form';
import { User, Building2, MapPin, Calendar, FileText, Users, ChevronDown, Info, Calendar1 } from 'lucide-react';
import { ProposalFormData, ProposalCustomer } from '@/types/component/proposals/TypeProposal';
import { CompanyUserStatus, CustomerStatus, CustomerType } from '@/lib/graphql/types/generated/graphql';
import { useGetAllCustomersQuery, useGetCompanyBasicDetailsQuery, useGetCompanyUsersQuery } from '@/lib/graphql/types/generated/hooks';
import ComponentTooltip from '@/component/common/ComponentTooltip';
import ComponentLoading from '@/component/common/ComponentLoading';
import ComponentNote from '@/component/common/ComponentNote';



const ComponentProposalBasicDetails: React.FC = () => {
    const { register, watch, setValue, formState: { errors } } = useFormContext<ProposalFormData>();

    const { data: companyData, loading: companyLoading, error: companyError } = useGetCompanyBasicDetailsQuery();
    const { data: companyUsersData, loading: companyUsersLoading, error: companyUsersError } = useGetCompanyUsersQuery();
    const { data: customersData, loading: customersLoading, error: customersError } = useGetAllCustomersQuery({
        variables: {
            status: CustomerStatus.Active, // Only fetch active customers
        }
    });

    const selectedCustomer = watch('customer');

    // Transform GraphQL customer data to ProposalCustomer format
    const customers: ProposalCustomer[] = customersData?.getCustomers.map(customer => {
        let name = '';
        let email = '';
        let phone = '';

        if (customer.__typename === 'CustomerBusiness') {
            name = customer.basicDetails.legalName;
            email = customer.basicDetails.contactDetails.email;
            phone = customer.basicDetails.contactDetails.phoneNo || '';
        } else if (customer.__typename === 'CustomerIndividual') {
            name = customer.basicDetails.contactDetails.name;
            email = customer.basicDetails.contactDetails.email;
            phone = customer.basicDetails.contactDetails.phoneNo || '';
        }

        return {
            id: customer.id,
            name,
            type: customer.type,
            status: customer.status,
            email,
            phone
        };
    }) || [];

    const handleCustomerChange = (customerId: string) => {
        const customer = customers.find(c => c.id === customerId);
        setValue('customer', customer || null);

        // Auto-populate customer billing address if customer is selected
        if (customer) {
            // TODO: In a real app, this would come from the customer data
            setValue('customerBillingAddress', {
                street: '123 Customer Street',
                city: 'Customer City',
                state: 'Customer State',
                zipCode: '12345',
                country: 'USA'
            });
        }
    };

    // Handle loading and error states
    if (customersLoading || companyLoading || companyUsersLoading) {
        return (
            <div className="mb-6">
                <h2 className="text-xl font-bold text-gray-900 mb-2">Proposal Basic Details</h2>
                <ComponentLoading message="Loading Proposal Information..." className="min-h-[200px]" />
            </div>
        );
    }

    if (customersError || companyError || companyUsersError) {
        return (
            <div className="mb-6">
                <h2 className="text-xl font-bold text-gray-900 mb-2">Proposal Basic Details</h2>
                <ComponentNote isError={true}>
                    Error loading customers: {customersError?.message || companyError?.message || companyUsersError?.message}
                </ComponentNote>
            </div>
        );
    }

    return (
        <div>
            <div className="mb-6">
                <h2 className="text-xl font-bold text-gray-900 mb-2">Proposal Basic Details</h2>
                <p className="text-gray-600">Customer details and proposal information</p>
            </div>
            <div className="">
                <div className="space-y-6">
                    {/* Customer Selection */}
                    <div className='bg-white rounded-lg'>
                        <div className="relative">
                            <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary h-4 w-4" />
                            <select
                                onChange={(e) => handleCustomerChange(e.target.value)}
                                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent appearance-none bg-white"
                            >
                                <option value="">Select a customer...</option>
                                {customers.map((customer) => (
                                    <option key={customer.id} value={customer.id}>
                                        {customer.name} ({customer.type})
                                    </option>
                                ))}
                            </select>
                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                                <ChevronDown className="h-4 w-4 text-gray-400" />
                            </div>
                        </div>
                    </div>

                    {/* Billing Addresses Row */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4 bg-white border border-gray-200 p-4 rounded-lg">
                            <div className="flex items-center gap-2 mb-3">
                                <Building2 className="h-4 w-4 text-secondary" />
                                Your Billing Address
                                <ComponentTooltip content='This is the billing address of your company. This can be changed in the Company Settings.'>
                                    <Info className="h-4 w-4 text-primary" />
                                </ComponentTooltip>
                            </div>
                            <div className="bg-gray-100 p-4 rounded-lg min-h-[120px]">
                                <div className="space-y-2 text-sm text-gray-600">
                                    <div>{companyData?.getCompany?.basicDetails?.name}</div>
                                    <div>{companyData?.getCompany?.basicDetails?.address}</div>
                                    <div>{companyData?.getCompany?.basicDetails?.phoneNumber}</div>
                                    <div>{companyData?.getCompany?.basicDetails?.email}</div>
                                </div>
                            </div>
                        </div>

                        {/* Customer Billing Address */}
                        <div className="space-y-4 bg-white border border-gray-200 p-4 rounded-lg">
                            <div className="flex items-center gap-2 mb-3">
                                <MapPin className="h-4 w-4 text-secondary" />
                                Customer Billing Address
                            </div>
                            <div className="space-y-3">
                                <input
                                    type="text"
                                    placeholder="Street Address"
                                    {...register('customerBillingAddress.street')}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm"
                                />
                                <div className="grid grid-cols-2 gap-2">
                                    <input
                                        type="text"
                                        placeholder="City"
                                        {...register('customerBillingAddress.city')}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm"
                                    />
                                    <input
                                        type="text"
                                        placeholder="State"
                                        {...register('customerBillingAddress.state')}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm"
                                    />
                                </div>
                                <div className="grid grid-cols-2 gap-2">
                                    <input
                                        type="text"
                                        placeholder="ZIP Code"
                                        {...register('customerBillingAddress.zipCode')}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm"
                                    />
                                    <input
                                        type="text"
                                        placeholder="Country"
                                        {...register('customerBillingAddress.country')}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Quote Details and Account Assignment Row */}
                    <div className="grid grid-cols-1  md:grid-cols-2 gap-6">
                        {/* Other basic details of the quote */}
                        <div className="space-y-4">
                            {/* <div className="border border-gray-200 p-4 rounded-lg">
                                <label className='flex items-center gap-2 mb-3'>
                                    <FileText className="h-4 w-4 text-gray-500" />
                                    <span>Proposal's Description</span>
                                </label>
                                <textarea
                                    placeholder="A proposal for website designing services"
                                    {...register('quoteDetails.description')}
                                    rows={3}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm resize-none"
                                />
                            </div> */}
                            <div className="space-y-3 bg-white border border-gray-200 p-4 rounded-lg">
                                <div>
                                    <div className='flex items-center gap-2 mb-3'>
                                        <Calendar1 className='h-4 w-4 text-secondary' />
                                        Dates
                                    </div>
                                    <div className="flex flex-col gap-1">
                                        <label className="text-xs text-gray-600">Proposal Date</label>
                                        <input
                                            type="date"
                                            {...register('quoteDetails.dateOfQuoteSending')}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm"
                                        />
                                    </div>
                                </div>
                                <div>
                                    <div className="flex flex-col gap-1">
                                        <label className="text-xs text-gray-600">Expiry date</label>
                                        <input
                                            type="date"
                                            {...register('quoteDetails.expiryDate')}
                                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Account Assignment */}
                        <div className="space-y-4 border bg-white border-gray-200 p-4 rounded-lg">
                            <div className="flex items-center gap-2 mb-3">
                                <Users className="h-4 w-4 text-secondary" />
                                <h3 className="text-sm text-gray-600">Account Assignment</h3>
                            </div>
                            <div className="space-y-3">
                                <div className="flex flex-col gap-1">
                                    <label className="text-xs text-gray-600">Sales Executive</label>
                                    <select
                                        {...register('accountAssignment.salesExecutive')}
                                        defaultValue={companyUsersData?.getCompany?.users?.[0]?.id}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm appearance-none bg-white"
                                    >
                                        {companyUsersData?.getCompany?.users?.filter(user => user?.status === CompanyUserStatus.Active)
                                            .map(user => (
                                                <option key={user?.id} value={user?.id}>
                                                    {user?.name}
                                                </option>
                                            ))}
                                    </select>
                                </div>
                                <div className="flex flex-col gap-1">
                                    <label className="text-xs text-gray-600">Customer Support Manager</label>
                                    <select
                                        {...register('accountAssignment.customerSupportManager')}
                                        defaultValue={companyUsersData?.getCompany?.users?.[0]?.id}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm appearance-none bg-white"
                                    >
                                        {companyUsersData?.getCompany?.users?.filter(user => user?.status === CompanyUserStatus.Active)
                                            .map(user => (
                                                <option key={user?.id} value={user?.id}>
                                                    {user?.name}
                                                </option>
                                            ))}
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ComponentProposalBasicDetails;
