import { CustomerType, CustomerStatus, Currency } from '@/lib/graphql/types/generated/graphql';

// Proposal status enum - will be replaced with GraphQL enum later
export enum ProposalStatus {
    DRAFT = 'DRAFT',
    PENDING = 'PENDING',
    APPROVED = 'APPROVED',
    REJECTED = 'REJECTED',
    EXPIRED = 'EXPIRED'
}

// Interface for customer selection in proposal
export interface ProposalCustomer {
    id: string;
    name: string;
    type: CustomerType;
    status: CustomerStatus;
    email: string;
    phone?: string;
}

// Interface for billing address
export interface BillingAddress {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
}

// Interface for account assignment
export interface AccountAssignment {
    salesExecutive: string;
    customerSupportManager: string;
}

// Interface for quote details
export interface QuoteDetails {
    description: string;
    dateOfQuoteSending: string;
    expiryDate: string;
}

// Interface for product in proposal
export interface ProposalProduct {
    id: string;
    name: string;
    dimensions: Array<{
        key: string;
        value: string;
    }>;
    listPrice: {
        value: number;
        currency: Currency;
    };
    discount?: {
        percentage: number;
        value: number;
        currency: Currency;
    };
    sellingPrice: {
        value: number;
        currency: Currency;
    };
    quantity: number;
    tax?: {
        percentage: number;
        value: number;
        currency: Currency;
    }
}

// Main proposal form data interface
export interface ProposalFormData {
    proposalId?: string;

    // Section 1: Customer and Basic Details
    customer: ProposalCustomer | null;
    companyBillingAddress: BillingAddress;
    customerBillingAddress: BillingAddress;
    quoteDetails: QuoteDetails;
    accountAssignment: AccountAssignment;

    // Section 2: Products
    products: ProposalProduct[];

    // Calculated totals
    subtotal: {
        value: number;
        currency: Currency;
    };

    totalDiscount: {
        value: number;
        currency: Currency;
    };

    totalTax: {
        value: number;
        currency: Currency;
    };
    total: {
        value: number;
        currency: Currency;
    };

    // Metadata
    status: ProposalStatus;
    createdDate: string;
    lastModified: string;
}

// Interface for product selection modal
export interface ProductSelectionModalProps {
    isOpen: boolean;
    onClose: () => void;
    onProductSelect: (product: ProposalProduct) => void;
    selectedProducts: ProposalProduct[];
}

// Interface for master product (from catalog)
export interface MasterProductForProposal {
    id: string;
    name: string;
    dimensions: Array<{
        key: string;
        value: string;
    }>;
    listPrice: {
        value: number;
        currency: Currency;
    };
    status: string;
}

// Props for proposal controls component
export interface ProposalControlsProps {
    searchQuery: string;
    onSearch: (e: React.ChangeEvent<HTMLInputElement>) => void;
    onAddProposal: () => void;
}

// Interface for proposal with basic details (for listing)
export interface ProposalWithBasicDetails {
    id: string;
    proposalNumber: string;
    customerName: string;
    customerType: CustomerType;
    customerStatus: CustomerStatus;
    salesExecutive: string;
    salesExecutiveId: string;
    totalValue: number;
    currency: string;
    status: ProposalStatus;
    createdDate: string;
    expiryDate: string;
    lastModified: string;
    customerAcceptedDate: string | null;
}
