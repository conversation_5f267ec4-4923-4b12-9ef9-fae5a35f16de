import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
import * as ApolloRea<PERSON>Hooks from "@apollo/client";
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = {
  [K in keyof T]: T[K];
};
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]?: Maybe<T[SubKey]>;
};
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]: Maybe<T[SubKey]>;
};
export type MakeEmpty<
  T extends { [key: string]: unknown },
  K extends keyof T,
> = { [_ in K]?: never };
export type Incremental<T> =
  | T
  | {
      [P in keyof T]?: P extends " $fragmentName" | "__typename" ? T[P] : never;
    };
const defaultOptions = {} as const;
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string };
  String: { input: string; output: string };
  Boolean: { input: boolean; output: boolean };
  Int: { input: number; output: number };
  Float: { input: number; output: number };
  Date: { input: string; output: string };
  DateTime: { input: string; output: string };
  _FieldSet: { input: any; output: any };
};

export type Amount = {
  readonly __typename?: "Amount";
  readonly currency: Currency;
  readonly value: Scalars["Float"]["output"];
};

export type AmountInput = {
  readonly currency: Currency;
  readonly value: Scalars["Float"]["input"];
};

/**  Company user ids */
export type AssignmentInput = {
  readonly accountManager?: InputMaybe<Scalars["ID"]["input"]>;
  readonly supportRepresentative?: InputMaybe<Scalars["ID"]["input"]>;
};

export type BusinessCustomerBasicDetails = {
  readonly __typename?: "BusinessCustomerBasicDetails";
  readonly address?: Maybe<Scalars["String"]["output"]>;
  readonly contactDetails: ContactDetails;
  readonly industry?: Maybe<Scalars["String"]["output"]>;
  readonly legalName: Scalars["String"]["output"];
  readonly referralSource?: Maybe<Scalars["String"]["output"]>;
  readonly size: EntitySize;
  readonly website: Scalars["String"]["output"];
};

export type BusinessCustomerBasicDetailsInput = {
  readonly address?: InputMaybe<Scalars["String"]["input"]>;
  readonly contactPersonDetails: ContactPersonDetailsInput;
  readonly industry?: InputMaybe<Scalars["String"]["input"]>;
  readonly legalName: Scalars["String"]["input"];
  readonly referralSource?: InputMaybe<Scalars["String"]["input"]>;
  readonly size: EntitySize;
  readonly website: Scalars["String"]["input"];
};

export enum ChargePolicy {
  Unit = "UNIT",
}

export type Company = {
  readonly __typename?: "Company";
  readonly basicDetails?: Maybe<CompanyBasicDetails>;
  readonly id: Scalars["ID"]["output"];
  readonly name?: Maybe<Scalars["String"]["output"]>;
  readonly onboarding?: Maybe<CompanyOnboarding>;
  readonly status?: Maybe<CompanyStatus>;
  readonly users?: Maybe<ReadonlyArray<Maybe<CompanyUser>>>;
};

export type CompanyBasicDetails = {
  readonly __typename?: "CompanyBasicDetails";
  readonly address?: Maybe<Scalars["String"]["output"]>;
  readonly email: Scalars["String"]["output"];
  readonly industry?: Maybe<Scalars["String"]["output"]>;
  readonly name: Scalars["String"]["output"];
  readonly phoneNumber?: Maybe<Scalars["String"]["output"]>;
  readonly size?: Maybe<EntitySize>;
  readonly website: Scalars["String"]["output"];
};

export type CompanyCreateBasicDetailsInput = {
  readonly address?: InputMaybe<Scalars["String"]["input"]>;
  readonly email: Scalars["String"]["input"];
  readonly industry?: InputMaybe<Scalars["String"]["input"]>;
  readonly name: Scalars["String"]["input"];
  readonly phoneNumber?: InputMaybe<Scalars["String"]["input"]>;
  readonly size?: InputMaybe<EntitySize>;
  readonly website: Scalars["String"]["input"];
};

export type CompanyEmail = Email & {
  readonly __typename?: "CompanyEmail";
  readonly attachments?: Maybe<ReadonlyArray<Scalars["String"]["output"]>>;
  readonly bcc?: Maybe<ReadonlyArray<Scalars["String"]["output"]>>;
  /**  optional as the template can support this as well */
  readonly body?: Maybe<Scalars["String"]["output"]>;
  readonly cc?: Maybe<ReadonlyArray<Scalars["String"]["output"]>>;
  /**  optional as the template can support this as well */
  readonly recipients: ReadonlyArray<Scalars["String"]["output"]>;
  readonly subject?: Maybe<Scalars["String"]["output"]>;
  readonly template: CompanyEmailTemplate;
  readonly type: EmailType;
};

export type CompanyEmailTemplate = EmailTemplate & {
  readonly __typename?: "CompanyEmailTemplate";
  readonly body: Scalars["String"]["output"];
  readonly fromName?: Maybe<Scalars["String"]["output"]>;
  readonly subject: Scalars["String"]["output"];
  readonly type: CompanyEmailTemplateType;
};

export enum CompanyEmailTemplateType {
  CompanyUserInvitationEmail = "COMPANY_USER_INVITATION_EMAIL",
  CompanyWelcomeEmail = "COMPANY_WELCOME_EMAIL",
}

export type CompanyOnboarding = {
  readonly __typename?: "CompanyOnboarding";
  readonly completedSteps: ReadonlyArray<Maybe<CompanyOnboardingStep>>;
  readonly id: Scalars["ID"]["output"];
  readonly pendingSteps: ReadonlyArray<Maybe<CompanyOnboardingStep>>;
};

export type CompanyOnboardingStep = {
  readonly __typename?: "CompanyOnboardingStep";
  readonly completed: Scalars["Boolean"]["output"];
  readonly mandatory: Scalars["Boolean"]["output"];
  readonly stepType: CompanyOnboardingStepType;
};

export enum CompanyOnboardingStepType {
  AccountingDetails = "ACCOUNTING_DETAILS",
  BasicDetails = "BASIC_DETAILS",
  CatalogDetails = "CATALOG_DETAILS",
  CompanyUsersDetails = "COMPANY_USERS_DETAILS",
}

export enum CompanyStatus {
  Active = "ACTIVE",
  Created = "CREATED",
  Inactive = "INACTIVE",
  Onboarding = "ONBOARDING",
}

export type CompanyUpdateBasicDetailsInput = {
  readonly address?: InputMaybe<Scalars["String"]["input"]>;
  readonly companyId: Scalars["ID"]["input"];
  readonly industry?: InputMaybe<Scalars["String"]["input"]>;
  readonly phoneNumber?: InputMaybe<Scalars["String"]["input"]>;
  readonly size?: InputMaybe<EntitySize>;
  readonly website: Scalars["String"]["input"];
};

export type CompanyUser = {
  readonly __typename?: "CompanyUser";
  readonly company: Company;
  readonly email: Scalars["String"]["output"];
  readonly id: Scalars["ID"]["output"];
  readonly name: Scalars["String"]["output"];
  readonly phoneNumber?: Maybe<Scalars["String"]["output"]>;
  readonly roles: ReadonlyArray<Maybe<CompanyUserRole>>;
  readonly status: CompanyUserStatus;
};

/**  input financial settings */
export type CompanyUserCreateInput = {
  readonly companyId: Scalars["ID"]["input"];
  readonly email: Scalars["String"]["input"];
  readonly name: Scalars["String"]["input"];
  readonly roles: ReadonlyArray<CompanyUserRole>;
};

export enum CompanyUserRole {
  AccountExecutive = "ACCOUNT_EXECUTIVE",
  Admin = "ADMIN",
  CharteredAccountant = "CHARTERED_ACCOUNTANT",
  CustomerSuccessManager = "CUSTOMER_SUCCESS_MANAGER",
}

export enum CompanyUserStatus {
  Active = "ACTIVE",
  Created = "CREATED",
  Inactive = "INACTIVE",
  Invited = "INVITED",
}

export type ContactDetails = {
  readonly __typename?: "ContactDetails";
  readonly contactType: ContactType;
  readonly email: Scalars["String"]["output"];
  readonly name: Scalars["String"]["output"];
  readonly phoneNo?: Maybe<Scalars["String"]["output"]>;
  readonly title?: Maybe<Scalars["String"]["output"]>;
};

export type ContactPersonDetailsInput = {
  readonly contactType: ContactType;
  readonly email: Scalars["String"]["input"];
  readonly name: Scalars["String"]["input"];
  readonly phoneNo?: InputMaybe<Scalars["String"]["input"]>;
  readonly title?: InputMaybe<Scalars["String"]["input"]>;
};

export enum ContactType {
  Business = "BUSINESS",
  Person = "PERSON",
}

export enum Currency {
  Aed = "AED",
  Inr = "INR",
  Usd = "USD",
}

export type CustomTag = {
  readonly __typename?: "CustomTag";
  readonly description?: Maybe<Scalars["String"]["output"]>;
  readonly id?: Maybe<Scalars["ID"]["output"]>;
  /**  to support client dto transformations */
  readonly key: Scalars["String"]["output"];
  readonly label: Scalars["String"]["output"];
  readonly type: CustomTagType;
  readonly value: Scalars["String"]["output"];
};

export type CustomTagInput = {
  readonly description?: InputMaybe<Scalars["String"]["input"]>;
  readonly id?: InputMaybe<Scalars["ID"]["input"]>;
  readonly key: Scalars["String"]["input"];
  readonly label: Scalars["String"]["input"];
  readonly type: CustomTagType;
  readonly value: Scalars["String"]["input"];
};

export enum CustomTagType {
  Boolean = "BOOLEAN",
  Date = "DATE",
  Numeric = "NUMERIC",
  Select = "SELECT",
  String = "STRING",
}

export type Customer = {
  readonly assignments: ReadonlyArray<Maybe<CustomerAssignment>>;
  readonly company: Company;
  readonly customTags: ReadonlyArray<Maybe<CustomTag>>;
  readonly documents: ReadonlyArray<Maybe<Document>>;
  readonly id: Scalars["ID"]["output"];
  readonly notes?: Maybe<ReadonlyArray<Maybe<Notes>>>;
  readonly plainTags?: Maybe<ReadonlyArray<Scalars["String"]["output"]>>;
  readonly stage: CustomerStage;
  readonly status: CustomerStatus;
  readonly type: CustomerType;
};

export type CustomerAssignment = {
  readonly __typename?: "CustomerAssignment";
  readonly accountManager?: Maybe<CompanyUser>;
  readonly supportRepresentative?: Maybe<CompanyUser>;
};

export type CustomerBusiness = Customer & {
  readonly __typename?: "CustomerBusiness";
  readonly assignments: ReadonlyArray<Maybe<CustomerAssignment>>;
  readonly basicDetails: BusinessCustomerBasicDetails;
  readonly company: Company;
  readonly customTags: ReadonlyArray<Maybe<CustomTag>>;
  readonly documents: ReadonlyArray<Maybe<Document>>;
  readonly id: Scalars["ID"]["output"];
  readonly notes?: Maybe<ReadonlyArray<Maybe<Notes>>>;
  readonly plainTags?: Maybe<ReadonlyArray<Scalars["String"]["output"]>>;
  readonly stage: CustomerStage;
  readonly status: CustomerStatus;
  readonly type: CustomerType;
};

export type CustomerIndividual = Customer & {
  readonly __typename?: "CustomerIndividual";
  readonly assignments: ReadonlyArray<Maybe<CustomerAssignment>>;
  readonly basicDetails: IndividualCustomerBasicDetails;
  readonly company: Company;
  readonly customTags: ReadonlyArray<Maybe<CustomTag>>;
  readonly documents: ReadonlyArray<Maybe<Document>>;
  readonly id: Scalars["ID"]["output"];
  readonly notes?: Maybe<ReadonlyArray<Maybe<Notes>>>;
  readonly plainTags?: Maybe<ReadonlyArray<Scalars["String"]["output"]>>;
  readonly stage: CustomerStage;
  readonly status: CustomerStatus;
  readonly type: CustomerType;
};

export type CustomerProduct = Product & {
  readonly __typename?: "CustomerProduct";
  /** User provided, can be null */
  readonly company: Company;
  readonly customTags?: Maybe<ReadonlyArray<CustomTag>>;
  readonly customer: Customer;
  readonly description?: Maybe<Scalars["String"]["output"]>;
  readonly dimensions?: Maybe<ReadonlyArray<Dimension>>;
  readonly documents?: Maybe<ReadonlyArray<Document>>;
  readonly id: Scalars["ID"]["output"];
  readonly name: Scalars["String"]["output"];
  readonly pricing: Pricing;
  readonly productCode?: Maybe<Scalars["String"]["output"]>;
  readonly productType: ProductType;
  readonly quote?: Maybe<Quote>;
  readonly status: ProductStatus;
  /** or quoteProduct? */
  readonly tax?: Maybe<ReadonlyArray<Tax>>;
  readonly version: Scalars["ID"]["output"];
};

export type CustomerProductToggleActivationInput = {
  readonly id: Scalars["ID"]["input"];
  readonly version?: InputMaybe<Scalars["ID"]["input"]>;
};

export type CustomerProductUpdateInput = {
  /**  Associated documents */
  readonly customTags?: InputMaybe<ReadonlyArray<CustomTagInput>>;
  /**  User provided unique identifier, can be null */
  readonly customerId: Scalars["ID"]["input"];
  /**  Required as unique identifier along with dimensions */
  readonly description?: InputMaybe<Scalars["String"]["input"]>;
  readonly dimensions?: InputMaybe<ReadonlyArray<DimensionInput>>;
  /**  Pricing information */
  readonly discount: DiscountInput;
  /**  Product dimensions/variants */
  readonly documents?: InputMaybe<ReadonlyArray<DocumentInput>>;
  readonly id: Scalars["ID"]["input"];
  /** To the code it's tagged to. */
  readonly name: Scalars["String"]["input"];
  /**  Custom tags for categorization */
  readonly pricing: PricingInput;
  readonly productCode?: InputMaybe<Scalars["String"]["input"]>;
  /**  Required to identify the customer */
  readonly quoteId: Scalars["ID"]["input"];
};

export enum CustomerStage {
  Churned = "CHURNED",
  ConversionFailed = "CONVERSION_FAILED",
  ConversionInProgress = "CONVERSION_IN_PROGRESS",
  Converted = "CONVERTED",
  Lead = "LEAD",
  Prospect = "PROSPECT",
}

export enum CustomerStatus {
  Active = "ACTIVE",
  Suspended = "SUSPENDED",
}

export enum CustomerType {
  Business = "BUSINESS",
  Individual = "INDIVIDUAL",
}

export type CustomerUpsertAdditionalDetailsInput = {
  readonly assignments?: InputMaybe<ReadonlyArray<AssignmentInput>>;
  readonly customTags?: InputMaybe<ReadonlyArray<CustomTagInput>>;
  readonly customerId: Scalars["ID"]["input"];
  readonly documents?: InputMaybe<ReadonlyArray<DocumentInput>>;
  readonly notes?: InputMaybe<ReadonlyArray<NotesInput>>;
};

export type CustomerUpsertBasicDetailsInput = {
  readonly businessCustomerDetails?: InputMaybe<BusinessCustomerBasicDetailsInput>;
  readonly customerId?: InputMaybe<Scalars["ID"]["input"]>;
  readonly customerStage: CustomerStage;
  readonly customerType: CustomerType;
  readonly individualCustomerDetails?: InputMaybe<IndividualCustomerBasicDetailsInput>;
};

export type Dimension = {
  readonly __typename?: "Dimension";
  readonly key: Scalars["String"]["output"];
  readonly value: Scalars["String"]["output"];
};

export type DimensionInput = {
  readonly key: Scalars["String"]["input"];
  readonly value: Scalars["String"]["input"];
};

export type Discount = {
  readonly __typename?: "Discount";
  /**
   *  Auto generated
   * name: String!
   */
  readonly description?: Maybe<Scalars["String"]["output"]>;
  readonly discountLevel: DiscountLevel;
  readonly discountType: DiscountType;
  readonly discountValue: DiscountValue;
  readonly id: Scalars["ID"]["output"];
};

export type DiscountInput = {
  readonly discountLevel: DiscountLevel;
  readonly discountType: DiscountType;
  readonly discountValue: DiscountValueInput;
};

export enum DiscountLevel {
  Product = "PRODUCT",
  Quote = "QUOTE",
}

export enum DiscountType {
  Amount = "AMOUNT",
  Percentage = "PERCENTAGE",
}

export type DiscountValue = {
  readonly __typename?: "DiscountValue";
  readonly percentage: Scalars["Float"]["output"];
  readonly value: Amount;
};

export type DiscountValueInput = {
  readonly amount: AmountInput;
  readonly percentage: Scalars["Float"]["input"];
};

export type Document = {
  readonly __typename?: "Document";
  readonly description?: Maybe<Scalars["String"]["output"]>;
  readonly file?: Maybe<FileLink>;
  readonly id?: Maybe<Scalars["ID"]["output"]>;
  readonly label?: Maybe<Scalars["String"]["output"]>;
  readonly tags: ReadonlyArray<Scalars["String"]["output"]>;
};

export type DocumentInput = {
  readonly file?: InputMaybe<FileLinkInput>;
  readonly id?: InputMaybe<Scalars["ID"]["input"]>;
};

export type Email = {
  readonly attachments?: Maybe<ReadonlyArray<Scalars["String"]["output"]>>;
  readonly bcc?: Maybe<ReadonlyArray<Scalars["String"]["output"]>>;
  /**  optional as the template can support this as well */
  readonly body?: Maybe<Scalars["String"]["output"]>;
  readonly cc?: Maybe<ReadonlyArray<Scalars["String"]["output"]>>;
  /**  optional as the template can support this as well */
  readonly recipients: ReadonlyArray<Scalars["String"]["output"]>;
  readonly subject?: Maybe<Scalars["String"]["output"]>;
  readonly template: EmailTemplate;
  readonly type: EmailType;
};

export type EmailTemplate = {
  readonly body: Scalars["String"]["output"];
  readonly fromName?: Maybe<Scalars["String"]["output"]>;
  readonly subject: Scalars["String"]["output"];
};

export enum EmailType {
  CompanyEmail = "COMPANY_EMAIL",
  OplatzEmail = "OPLATZ_EMAIL",
}

export enum EntitySize {
  GreaterThanThousand = "GREATER_THAN_THOUSAND",
  HundredToThousand = "HUNDRED_TO_THOUSAND",
  LessThanTen = "LESS_THAN_TEN",
  TenToHundred = "TEN_TO_HUNDRED",
  Unknown = "UNKNOWN",
}

export enum ErrorDetail {
  /**
   * The deadline expired before the operation could complete.
   *
   * For operations that change the state of the system, this error
   * may be returned even if the operation has completed successfully.
   * For example, a successful response from a server could have been
   * delayed long enough for the deadline to expire.
   *
   * HTTP Mapping: 504 Gateway Timeout
   * Error Type: UNAVAILABLE
   */
  DeadlineExceeded = "DEADLINE_EXCEEDED",
  /**
   * The server detected that the client is exhibiting a behavior that
   * might be generating excessive load.
   *
   * HTTP Mapping: 420 Enhance Your Calm
   * Error Type: UNAVAILABLE
   */
  EnhanceYourCalm = "ENHANCE_YOUR_CALM",
  /**
   * The requested field is not found in the schema.
   *
   * This differs from `NOT_FOUND` in that `NOT_FOUND` should be used when a
   * query is valid, but is unable to return a result (if, for example, a
   * specific video id doesn't exist). `FIELD_NOT_FOUND` is intended to be
   * returned by the server to signify that the requested field is not known to exist.
   * This may be returned in lieu of failing the entire query.
   * See also `PERMISSION_DENIED` for cases where the
   * requested field is invalid only for the given user or class of users.
   *
   * HTTP Mapping: 404 Not Found
   * Error Type: BAD_REQUEST
   */
  FieldNotFound = "FIELD_NOT_FOUND",
  /**
   * The client specified an invalid argument.
   *
   * Note that this differs from `FAILED_PRECONDITION`.
   * `INVALID_ARGUMENT` indicates arguments that are problematic
   * regardless of the state of the system (e.g., a malformed file name).
   *
   * HTTP Mapping: 400 Bad Request
   * Error Type: BAD_REQUEST
   */
  InvalidArgument = "INVALID_ARGUMENT",
  /**
   * The provided cursor is not valid.
   *
   * The most common usage for this error is when a client is paginating
   * through a list that uses stateful cursors. In that case, the provided
   * cursor may be expired.
   *
   * HTTP Mapping: 404 Not Found
   * Error Type: NOT_FOUND
   */
  InvalidCursor = "INVALID_CURSOR",
  /**
   * Unable to perform operation because a required resource is missing.
   *
   * Example: Client is attempting to refresh a list, but the specified
   * list is expired. This requires an action by the client to get a new list.
   *
   * If the user is simply trying GET a resource that is not found,
   * use the NOT_FOUND error type. FAILED_PRECONDITION.MISSING_RESOURCE
   * is to be used particularly when the user is performing an operation
   * that requires a particular resource to exist.
   *
   * HTTP Mapping: 400 Bad Request or 500 Internal Server Error
   * Error Type: FAILED_PRECONDITION
   */
  MissingResource = "MISSING_RESOURCE",
  /**
   * Service Error.
   *
   * There is a problem with an upstream service.
   *
   * This may be returned if a gateway receives an unknown error from a service
   * or if a service is unreachable.
   * If a request times out which waiting on a response from a service,
   * `DEADLINE_EXCEEDED` may be returned instead.
   * If a service returns a more specific error Type, the specific error Type may
   * be returned instead.
   *
   * HTTP Mapping: 502 Bad Gateway
   * Error Type: UNAVAILABLE
   */
  ServiceError = "SERVICE_ERROR",
  /**
   * Request failed due to network errors.
   *
   * HTTP Mapping: 503 Unavailable
   * Error Type: UNAVAILABLE
   */
  TcpFailure = "TCP_FAILURE",
  /**
   * Request throttled based on server concurrency limits.
   *
   * HTTP Mapping: 503 Unavailable
   * Error Type: UNAVAILABLE
   */
  ThrottledConcurrency = "THROTTLED_CONCURRENCY",
  /**
   * Request throttled based on server CPU limits
   *
   * HTTP Mapping: 503 Unavailable.
   * Error Type: UNAVAILABLE
   */
  ThrottledCpu = "THROTTLED_CPU",
  /**
   * The server detected that the client is exhibiting a behavior that
   * might be generating excessive load.
   *
   * HTTP Mapping: 429 Too Many Requests
   * Error Type: UNAVAILABLE
   */
  TooManyRequests = "TOO_MANY_REQUESTS",
  /**
   * The operation is not implemented or is not currently supported/enabled.
   *
   * HTTP Mapping: 501 Not Implemented
   * Error Type: BAD_REQUEST
   */
  Unimplemented = "UNIMPLEMENTED",
  /**
   * Unknown error.
   *
   * This error should only be returned when no other error detail applies.
   * If a client sees an unknown errorDetail, it will be interpreted as UNKNOWN.
   *
   * HTTP Mapping: 500 Internal Server Error
   */
  Unknown = "UNKNOWN",
}

export enum ErrorType {
  /**
   * Bad Request.
   *
   * There is a problem with the request.
   * Retrying the same request is not likely to succeed.
   * An example would be a query or argument that cannot be deserialized.
   *
   * HTTP Mapping: 400 Bad Request
   */
  BadRequest = "BAD_REQUEST",
  /**
   * The operation was rejected because the system is not in a state
   * required for the operation's execution.  For example, the directory
   * to be deleted is non-empty, an rmdir operation is applied to
   * a non-directory, etc.
   *
   * Service implementers can use the following guidelines to decide
   * between `FAILED_PRECONDITION` and `UNAVAILABLE`:
   *
   * - Use `UNAVAILABLE` if the client can retry just the failing call.
   * - Use `FAILED_PRECONDITION` if the client should not retry until
   * the system state has been explicitly fixed.  E.g., if an "rmdir"
   *      fails because the directory is non-empty, `FAILED_PRECONDITION`
   * should be returned since the client should not retry unless
   * the files are deleted from the directory.
   *
   * HTTP Mapping: 400 Bad Request or 500 Internal Server Error
   */
  FailedPrecondition = "FAILED_PRECONDITION",
  /**
   * Internal error.
   *
   * An unexpected internal error was encountered. This means that some
   * invariants expected by the underlying system have been broken.
   * This error code is reserved for serious errors.
   *
   * HTTP Mapping: 500 Internal Server Error
   */
  Internal = "INTERNAL",
  /**
   * The requested entity was not found.
   *
   * This could apply to a resource that has never existed (e.g. bad resource id),
   * or a resource that no longer exists (e.g. cache expired.)
   *
   * Note to server developers: if a request is denied for an entire class
   * of users, such as gradual feature rollout or undocumented allowlist,
   * `NOT_FOUND` may be used. If a request is denied for some users within
   * a class of users, such as user-based access control, `PERMISSION_DENIED`
   * must be used.
   *
   * HTTP Mapping: 404 Not Found
   */
  NotFound = "NOT_FOUND",
  /**
   * The caller does not have permission to execute the specified
   * operation.
   *
   * `PERMISSION_DENIED` must not be used for rejections
   * caused by exhausting some resource or quota.
   * `PERMISSION_DENIED` must not be used if the caller
   * cannot be identified (use `UNAUTHENTICATED`
   * instead for those errors).
   *
   * This error Type does not imply the
   * request is valid or the requested entity exists or satisfies
   * other pre-conditions.
   *
   * HTTP Mapping: 403 Forbidden
   */
  PermissionDenied = "PERMISSION_DENIED",
  /**
   * The request does not have valid authentication credentials.
   *
   * This is intended to be returned only for routes that require
   * authentication.
   *
   * HTTP Mapping: 401 Unauthorized
   */
  Unauthenticated = "UNAUTHENTICATED",
  /**
   * Currently Unavailable.
   *
   * The service is currently unavailable.  This is most likely a
   * transient condition, which can be corrected by retrying with
   * a backoff.
   *
   * HTTP Mapping: 503 Unavailable
   */
  Unavailable = "UNAVAILABLE",
  /**
   * Unknown error.
   *
   * For example, this error may be returned when
   * an error code received from another address space belongs to
   * an error space that is not known in this address space.  Also
   * errors raised by APIs that do not return enough error information
   * may be converted to this error.
   *
   * If a client sees an unknown errorType, it will be interpreted as UNKNOWN.
   * Unknown errors MUST NOT trigger any special behavior. These MAY be treated
   * by an implementation as being equivalent to INTERNAL.
   *
   * When possible, a more specific error should be provided.
   *
   * HTTP Mapping: 520 Unknown Error
   */
  Unknown = "UNKNOWN",
}

export type FileLink = {
  readonly __typename?: "FileLink";
  readonly signedReadURL?: Maybe<Scalars["String"]["output"]>;
  readonly signedWriteURL?: Maybe<Scalars["String"]["output"]>;
};

export type FileLinkInput = {
  readonly signedReadURL?: InputMaybe<Scalars["String"]["input"]>;
  readonly signedWriteURL?: InputMaybe<Scalars["String"]["input"]>;
};

export type GetCustomersQueryFilterInput = {
  readonly customerType?: InputMaybe<CustomerType>;
  readonly stage?: InputMaybe<CustomerStage>;
  readonly status?: InputMaybe<CustomerStatus>;
};

export type GetMasterProductFilters = {
  readonly status?: InputMaybe<ProductStatus>;
};

export type GetQuotesFilters = {
  readonly status?: InputMaybe<QuoteStatus>;
};

export type IndividualCustomerBasicDetails = {
  readonly __typename?: "IndividualCustomerBasicDetails";
  readonly address?: Maybe<Scalars["String"]["output"]>;
  readonly contactDetails: ContactDetails;
  readonly referralSource?: Maybe<Scalars["String"]["output"]>;
};

export type IndividualCustomerBasicDetailsInput = {
  readonly address?: InputMaybe<Scalars["String"]["input"]>;
  readonly emailAddress: Scalars["String"]["input"];
  readonly fullName: Scalars["String"]["input"];
  readonly phoneNumber?: InputMaybe<Scalars["String"]["input"]>;
  readonly referralSource?: InputMaybe<Scalars["String"]["input"]>;
};

export type Margin = {
  readonly __typename?: "Margin";
  readonly absoluteAmount: Amount;
  readonly percentage: Scalars["Float"]["output"];
};

export type MarginInput = {
  readonly absoluteAmount: AmountInput;
  readonly percentage: Scalars["Float"]["input"];
};

export type MasterProduct = Product & {
  readonly __typename?: "MasterProduct";
  /** User provided, can be null */
  readonly company: Company;
  readonly customTags?: Maybe<ReadonlyArray<CustomTag>>;
  readonly description?: Maybe<Scalars["String"]["output"]>;
  readonly dimensions?: Maybe<ReadonlyArray<Dimension>>;
  readonly documents?: Maybe<ReadonlyArray<Document>>;
  readonly id: Scalars["ID"]["output"];
  readonly name: Scalars["String"]["output"];
  readonly pricing: Pricing;
  readonly productCode?: Maybe<Scalars["String"]["output"]>;
  readonly productType: ProductType;
  readonly status: ProductStatus;
  readonly tax?: Maybe<ReadonlyArray<Tax>>;
  readonly version: Scalars["ID"]["output"];
};

export type MasterProductToggleActivationInput = {
  readonly id: Scalars["ID"]["input"];
  readonly version?: InputMaybe<Scalars["ID"]["input"]>;
};

export type MasterProductUpsertAdditionalDetailsInput = {
  readonly customTags?: InputMaybe<ReadonlyArray<CustomTagInput>>;
  readonly documents?: InputMaybe<ReadonlyArray<DocumentInput>>;
  readonly masterProductId: Scalars["ID"]["input"];
};

export type MasterProductUpsertInput = {
  readonly description?: InputMaybe<Scalars["String"]["input"]>;
  readonly dimensions?: InputMaybe<ReadonlyArray<DimensionInput>>;
  readonly id?: InputMaybe<Scalars["ID"]["input"]>;
  readonly name: Scalars["String"]["input"];
  readonly pricing: PricingInput;
  readonly productCode: Scalars["String"]["input"];
};

export type Mutation = {
  readonly __typename?: "Mutation";
  readonly companyCreateBasicDetails?: Maybe<Company>;
  readonly companyUpdateBasicDetails?: Maybe<Company>;
  readonly companyUserActivate: CompanyUser;
  readonly companyUserCreate: CompanyUser;
  readonly companyUserDeactivate: TaskResponse;
  readonly companyUserInvite: CompanyUser;
  readonly customerProductToggleActivation: CustomerProduct;
  readonly customerProductUpdate: CustomerProduct;
  readonly customerUpsertAdditionalDetails?: Maybe<Customer>;
  readonly customerUpsertBasicDetails?: Maybe<Customer>;
  readonly masterProductToggleActivation: MasterProduct;
  readonly masterProductUpsertAdditionalDetails: MasterProduct;
  readonly masterProductUpsertDetails: MasterProduct;
  readonly ok?: Maybe<Scalars["Boolean"]["output"]>;
  readonly quoteUpsert: Quote;
};

export type MutationCompanyCreateBasicDetailsArgs = {
  input: CompanyCreateBasicDetailsInput;
};

export type MutationCompanyUpdateBasicDetailsArgs = {
  input: CompanyUpdateBasicDetailsInput;
};

export type MutationCompanyUserCreateArgs = {
  input: CompanyUserCreateInput;
};

export type MutationCompanyUserDeactivateArgs = {
  companyUserId: Scalars["ID"]["input"];
};

export type MutationCompanyUserInviteArgs = {
  companyUserId: Scalars["ID"]["input"];
};

export type MutationCustomerProductToggleActivationArgs = {
  input: CustomerProductToggleActivationInput;
};

export type MutationCustomerProductUpdateArgs = {
  input: CustomerProductUpdateInput;
};

export type MutationCustomerUpsertAdditionalDetailsArgs = {
  input: CustomerUpsertAdditionalDetailsInput;
};

export type MutationCustomerUpsertBasicDetailsArgs = {
  input: CustomerUpsertBasicDetailsInput;
};

export type MutationMasterProductToggleActivationArgs = {
  input: MasterProductToggleActivationInput;
};

export type MutationMasterProductUpsertAdditionalDetailsArgs = {
  input: MasterProductUpsertAdditionalDetailsInput;
};

export type MutationMasterProductUpsertDetailsArgs = {
  input: MasterProductUpsertInput;
};

export type MutationQuoteUpsertArgs = {
  input: QuoteUpsertInput;
};

export type Notes = {
  readonly __typename?: "Notes";
  /**  to support client dto transformations */
  readonly content: Scalars["String"]["output"];
  readonly id?: Maybe<Scalars["ID"]["output"]>;
  readonly tags: ReadonlyArray<Scalars["String"]["output"]>;
};

export type NotesInput = {
  readonly content: Scalars["String"]["input"];
  readonly id?: InputMaybe<Scalars["ID"]["input"]>;
  readonly tags: ReadonlyArray<Scalars["String"]["input"]>;
};

export type OplatzEmail = Email & {
  readonly __typename?: "OplatzEmail";
  readonly attachments?: Maybe<ReadonlyArray<Scalars["String"]["output"]>>;
  readonly bcc?: Maybe<ReadonlyArray<Scalars["String"]["output"]>>;
  /**  optional as the template can support this as well */
  readonly body?: Maybe<Scalars["String"]["output"]>;
  readonly cc?: Maybe<ReadonlyArray<Scalars["String"]["output"]>>;
  /**  optional as the template can support this as well */
  readonly recipients: ReadonlyArray<Scalars["String"]["output"]>;
  readonly subject?: Maybe<Scalars["String"]["output"]>;
  readonly template: OplatzEmailTemplate;
  readonly type: EmailType;
};

export type OplatzEmailTemplate = EmailTemplate & {
  readonly __typename?: "OplatzEmailTemplate";
  readonly body: Scalars["String"]["output"];
  readonly fromName?: Maybe<Scalars["String"]["output"]>;
  readonly subject: Scalars["String"]["output"];
  readonly type: OplatzEmailTemplateType;
};

export enum OplatzEmailTemplateType {
  OplatzCompanyCreated = "OPLATZ_COMPANY_CREATED",
}

export type Pricing = {
  readonly __typename?: "Pricing";
  readonly chargePolicy: ChargePolicy;
  readonly costPrice: Amount;
  readonly discount: ReadonlyArray<Maybe<Discount>>;
  readonly id: Scalars["ID"]["output"];
  /**  price at which we buy from vendor or cost of rendering the service */
  readonly listPrice: Amount;
  /**  price @ which we sell to customer after discount, will be same as list price by default */
  readonly margin: Margin;
  /**  default price to sell at to customer */
  readonly sellingPrice: Amount;
  /**
   *  margin = (sellingPrice - costPrice) / sellingPrice
   * billingFrequency
   */
  readonly unit: ProductUnit;
};

export type PricingInput = {
  readonly chargePolicy: ChargePolicy;
  readonly costPrice: AmountInput;
  /**  unit of measurement for the product, e.g: 1 */
  readonly discounts?: InputMaybe<ReadonlyArray<DiscountInput>>;
  readonly id?: InputMaybe<Scalars["ID"]["input"]>;
  /**  price at which we buy from vendor or cost of rendering the service */
  readonly listPrice: AmountInput;
  /**  price @ which we sell to customer after discount, will be same as list price by default */
  readonly productUnit: ProductUnitInput;
  /**  default price to sell at to customer */
  readonly sellingPrice: AmountInput;
  /**  discounts applicable on the product, e.g: 10% off */
  readonly tax?: InputMaybe<ReadonlyArray<TaxInput>>;
};

export type Product = {
  readonly company: Company;
  readonly customTags?: Maybe<ReadonlyArray<CustomTag>>;
  /**  Will be used as unique identifier along with dimensions, if productCode is null */
  readonly description?: Maybe<Scalars["String"]["output"]>;
  readonly dimensions?: Maybe<ReadonlyArray<Dimension>>;
  readonly documents?: Maybe<ReadonlyArray<Document>>;
  readonly id: Scalars["ID"]["output"];
  /**  User provided, unique identifier along with dimensions, it can be null */
  readonly name: Scalars["String"]["output"];
  readonly pricing: Pricing;
  readonly productCode?: Maybe<Scalars["String"]["output"]>;
  readonly productType: ProductType;
  readonly status: ProductStatus;
  readonly tax?: Maybe<ReadonlyArray<Tax>>;
  readonly version: Scalars["ID"]["output"];
};

export enum ProductStatus {
  Active = "ACTIVE",
  Created = "CREATED",
  Inactive = "INACTIVE",
}

export enum ProductType {
  Customer = "CUSTOMER",
  Master = "MASTER",
  Quote = "QUOTE",
}

export type ProductUnit = {
  readonly __typename?: "ProductUnit";
  readonly unit: Scalars["Float"]["output"];
  /**  unit of measurement for the product, e.g: 1 */
  readonly unitType: Scalars["String"]["output"];
};

export type ProductUnitInput = {
  readonly unit: Scalars["Float"]["input"];
  /**  unit of measurement for the product, e.g: 1 */
  readonly unitType: Scalars["String"]["input"];
};

export type Query = {
  readonly __typename?: "Query";
  readonly _service: _Service;
  readonly getCompany?: Maybe<Company>;
  readonly getCompanyUser?: Maybe<CompanyUser>;
  readonly getCustomer?: Maybe<Customer>;
  readonly getCustomers: ReadonlyArray<Customer>;
  readonly masterProductGetById: MasterProduct;
  readonly masterProductsGet: ReadonlyArray<MasterProduct>;
  readonly ok?: Maybe<Scalars["Boolean"]["output"]>;
  readonly quotesGet: ReadonlyArray<Quote>;
};

export type QueryGetCustomerArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryGetCustomersArgs = {
  filters?: InputMaybe<GetCustomersQueryFilterInput>;
};

export type QueryMasterProductGetByIdArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryMasterProductsGetArgs = {
  filters?: InputMaybe<GetMasterProductFilters>;
};

export type QueryQuotesGetArgs = {
  filters?: InputMaybe<GetQuotesFilters>;
};

export type Quote = {
  readonly __typename?: "Quote";
  readonly address: QuoteAddress;
  readonly assignedCompanyUser?: Maybe<CompanyUser>;
  /** calculated value from the quote product items */
  readonly assignments?: Maybe<ReadonlyArray<QuoteAssignment>>;
  readonly company: Company;
  readonly createdDate: Scalars["DateTime"]["output"];
  readonly customTags?: Maybe<ReadonlyArray<CustomTag>>;
  readonly customer: Customer;
  readonly date: QuoteDate;
  readonly description?: Maybe<Scalars["String"]["output"]>;
  /** calculated value from the quote product items */
  readonly discounts: ReadonlyArray<Discount>;
  readonly documents?: Maybe<ReadonlyArray<Document>>;
  readonly id: Scalars["ID"]["output"];
  /** the person example, sales etc to which this quote is assigned. */
  readonly products: ReadonlyArray<QuoteProduct>;
  readonly quoteTotalDiscountAmount: Amount;
  readonly quoteTotalListPrice: Amount;
  /**  quote products.listPrice */
  readonly quoteTotalSellingPrice: Amount;
  readonly quoteTotalTaxAmount: Amount;
  readonly status: QuoteStatus;
  readonly version: Scalars["ID"]["output"];
};

export type QuoteAddress = {
  readonly __typename?: "QuoteAddress";
  readonly fromAddress: Scalars["String"]["output"];
  readonly toAddress: Scalars["String"]["output"];
};

export type QuoteAddressInput = {
  readonly fromAddress: Scalars["String"]["input"];
  readonly toAddress: Scalars["String"]["input"];
};

export type QuoteAssignment = {
  readonly __typename?: "QuoteAssignment";
  readonly customerSuccessManger?: Maybe<CompanyUser>;
  readonly salesExecutive?: Maybe<CompanyUser>;
};

export type QuoteAssignmentInput = {
  readonly customerSuccessManger?: InputMaybe<Scalars["ID"]["input"]>;
  readonly salesExecutive?: InputMaybe<Scalars["ID"]["input"]>;
};

export type QuoteDate = {
  readonly __typename?: "QuoteDate";
  readonly validFrom: Scalars["DateTime"]["output"];
  readonly validTill?: Maybe<Scalars["DateTime"]["output"]>;
};

export type QuoteDateInput = {
  readonly validFrom: Scalars["DateTime"]["input"];
  readonly validTill?: InputMaybe<Scalars["DateTime"]["input"]>;
};

export type QuoteProduct = Product & {
  readonly __typename?: "QuoteProduct";
  readonly company: Company;
  readonly customTags?: Maybe<ReadonlyArray<CustomTag>>;
  readonly description?: Maybe<Scalars["String"]["output"]>;
  readonly dimensions?: Maybe<ReadonlyArray<Dimension>>;
  readonly documents?: Maybe<ReadonlyArray<Document>>;
  readonly id: Scalars["ID"]["output"];
  /** User provided, can be null */
  readonly name: Scalars["String"]["output"];
  readonly pricing: Pricing;
  readonly productCode?: Maybe<Scalars["String"]["output"]>;
  readonly productType: ProductType;
  readonly quote: Quote;
  readonly referenceProduct?: Maybe<QuoteProductReferenceProduct>;
  readonly status: ProductStatus;
  readonly tax?: Maybe<ReadonlyArray<Tax>>;
  /** we don't want that, right? Shall we always keep it CREATED? */
  readonly version: Scalars["ID"]["output"];
};

export type QuoteProductCreateInput = {
  readonly customTags?: InputMaybe<ReadonlyArray<CustomTagInput>>;
  readonly customerId: Scalars["ID"]["input"];
  readonly description?: InputMaybe<Scalars["String"]["input"]>;
  readonly dimensions?: InputMaybe<ReadonlyArray<DimensionInput>>;
  readonly documents?: InputMaybe<ReadonlyArray<DocumentInput>>;
  /** non mandatory. */
  readonly name: Scalars["String"]["input"];
  readonly pricing: PricingInput;
  readonly productCode: Scalars["String"]["input"];
  readonly referenceProduct?: InputMaybe<QuoteProductReferenceProductInput>;
  readonly tax?: InputMaybe<ReadonlyArray<TaxInput>>;
};

export type QuoteProductReferenceProduct = {
  readonly __typename?: "QuoteProductReferenceProduct";
  readonly id?: Maybe<Scalars["ID"]["output"]>;
  readonly type?: Maybe<ProductType>;
};

export type QuoteProductReferenceProductInput = {
  readonly id: Scalars["ID"]["input"];
  readonly type: ProductType;
};

export enum QuoteStatus {
  Accepted = "ACCEPTED",
  Activated = "ACTIVATED",
  Created = "CREATED",
  Deleted = "DELETED",
  Rejected = "REJECTED",
  Sent = "SENT",
}

export type QuoteUpsertInput = {
  readonly addressInput: QuoteAddressInput;
  readonly assignments?: InputMaybe<ReadonlyArray<QuoteAssignmentInput>>;
  readonly customTags?: InputMaybe<ReadonlyArray<CustomTagInput>>;
  readonly customerId: Scalars["ID"]["input"];
  readonly dateInput: QuoteDateInput;
  readonly description?: InputMaybe<Scalars["String"]["input"]>;
  readonly discounts?: InputMaybe<ReadonlyArray<DiscountInput>>;
  readonly documents?: InputMaybe<ReadonlyArray<DocumentInput>>;
  readonly id?: InputMaybe<Scalars["ID"]["input"]>;
  readonly paymentTerms?: InputMaybe<Scalars["String"]["input"]>;
  readonly quoteProductsInput: ReadonlyArray<QuoteProductCreateInput>;
};

export type TaskResponse = {
  readonly __typename?: "TaskResponse";
  readonly message: Scalars["String"]["output"];
  readonly success: Scalars["Boolean"]["output"];
};

export type Tax = {
  readonly __typename?: "Tax";
  readonly amount: Amount;
  readonly name?: Maybe<Scalars["String"]["output"]>;
  readonly percentage: Scalars["Float"]["output"];
};

export type TaxInput = {
  readonly amount: AmountInput;
  readonly name?: InputMaybe<Scalars["String"]["input"]>;
  readonly percentage: Scalars["Float"]["input"];
};

export type _Service = {
  readonly __typename?: "_Service";
  readonly sdl: Scalars["String"]["output"];
};

export type CompanyUserCreateMutationVariables = Exact<{
  input: CompanyUserCreateInput;
}>;

export type CompanyUserCreateMutation = {
  readonly __typename?: "Mutation";
  readonly companyUserCreate: {
    readonly __typename?: "CompanyUser";
    readonly id: string;
    readonly name: string;
    readonly email: string;
    readonly status: CompanyUserStatus;
    readonly roles: ReadonlyArray<CompanyUserRole | null>;
  };
};

export type CompanyUserInviteMutationVariables = Exact<{
  companyUserId: Scalars["ID"]["input"];
}>;

export type CompanyUserInviteMutation = {
  readonly __typename?: "Mutation";
  readonly companyUserInvite: {
    readonly __typename?: "CompanyUser";
    readonly id: string;
    readonly name: string;
    readonly email: string;
    readonly status: CompanyUserStatus;
    readonly roles: ReadonlyArray<CompanyUserRole | null>;
  };
};

export type CompanyUserDeactivateMutationVariables = Exact<{
  companyUserId: Scalars["ID"]["input"];
}>;

export type CompanyUserDeactivateMutation = {
  readonly __typename?: "Mutation";
  readonly companyUserDeactivate: {
    readonly __typename?: "TaskResponse";
    readonly success: boolean;
    readonly message: string;
  };
};

export type CompanyUserActivateMutationVariables = Exact<{
  [key: string]: never;
}>;

export type CompanyUserActivateMutation = {
  readonly __typename?: "Mutation";
  readonly companyUserActivate: {
    readonly __typename?: "CompanyUser";
    readonly id: string;
    readonly name: string;
    readonly email: string;
    readonly status: CompanyUserStatus;
    readonly roles: ReadonlyArray<CompanyUserRole | null>;
  };
};

export type CreateCompanyBasicDetailsMutationVariables = Exact<{
  input: CompanyCreateBasicDetailsInput;
}>;

export type CreateCompanyBasicDetailsMutation = {
  readonly __typename?: "Mutation";
  readonly companyCreateBasicDetails?: {
    readonly __typename?: "Company";
    readonly id: string;
    readonly basicDetails?: {
      readonly __typename?: "CompanyBasicDetails";
      readonly name: string;
      readonly address?: string | null;
      readonly phoneNumber?: string | null;
      readonly email: string;
      readonly website: string;
      readonly industry?: string | null;
      readonly size?: EntitySize | null;
    } | null;
  } | null;
};

export type UpdateCompanyBasicDetailsMutationVariables = Exact<{
  input: CompanyUpdateBasicDetailsInput;
}>;

export type UpdateCompanyBasicDetailsMutation = {
  readonly __typename?: "Mutation";
  readonly companyUpdateBasicDetails?: {
    readonly __typename?: "Company";
    readonly id: string;
    readonly basicDetails?: {
      readonly __typename?: "CompanyBasicDetails";
      readonly name: string;
      readonly address?: string | null;
      readonly phoneNumber?: string | null;
      readonly email: string;
      readonly website: string;
      readonly industry?: string | null;
      readonly size?: EntitySize | null;
    } | null;
  } | null;
};

export type CustomerUpsertBasicDetailsMutationVariables = Exact<{
  input: CustomerUpsertBasicDetailsInput;
}>;

export type CustomerUpsertBasicDetailsMutation = {
  readonly __typename?: "Mutation";
  readonly customerUpsertBasicDetails?:
    | { readonly __typename?: "CustomerBusiness"; readonly id: string }
    | { readonly __typename?: "CustomerIndividual"; readonly id: string }
    | null;
};

export type CustomerUpsertAdditionalDetailsMutationVariables = Exact<{
  input: CustomerUpsertAdditionalDetailsInput;
}>;

export type CustomerUpsertAdditionalDetailsMutation = {
  readonly __typename?: "Mutation";
  readonly customerUpsertAdditionalDetails?:
    | { readonly __typename?: "CustomerBusiness"; readonly id: string }
    | { readonly __typename?: "CustomerIndividual"; readonly id: string }
    | null;
};

export type MasterProductUpsertDetailsMutationVariables = Exact<{
  input: MasterProductUpsertInput;
}>;

export type MasterProductUpsertDetailsMutation = {
  readonly __typename?: "Mutation";
  readonly masterProductUpsertDetails: {
    readonly __typename?: "MasterProduct";
    readonly id: string;
    readonly pricing: { readonly __typename?: "Pricing"; readonly id: string };
  };
};

export type MasterProductUpsertAdditionalDetailsMutationVariables = Exact<{
  input: MasterProductUpsertAdditionalDetailsInput;
}>;

export type MasterProductUpsertAdditionalDetailsMutation = {
  readonly __typename?: "Mutation";
  readonly masterProductUpsertAdditionalDetails: {
    readonly __typename?: "MasterProduct";
    readonly id: string;
    readonly pricing: { readonly __typename?: "Pricing"; readonly id: string };
  };
};

export type ProposalProductUpsertMutationVariables = Exact<{
  input: QuoteUpsertInput;
}>;

export type ProposalProductUpsertMutation = {
  readonly __typename?: "Mutation";
  readonly quoteUpsert: {
    readonly __typename?: "Quote";
    readonly id: string;
    readonly status: QuoteStatus;
  };
};

export type OkQueryVariables = Exact<{ [key: string]: never }>;

export type OkQuery = {
  readonly __typename?: "Query";
  readonly ok?: boolean | null;
};

export type GetCompanyQueryVariables = Exact<{ [key: string]: never }>;

export type GetCompanyQuery = {
  readonly __typename?: "Query";
  readonly getCompany?: {
    readonly __typename?: "Company";
    readonly id: string;
    readonly name?: string | null;
    readonly status?: CompanyStatus | null;
    readonly onboarding?: {
      readonly __typename?: "CompanyOnboarding";
      readonly id: string;
      readonly pendingSteps: ReadonlyArray<{
        readonly __typename?: "CompanyOnboardingStep";
        readonly stepType: CompanyOnboardingStepType;
        readonly mandatory: boolean;
        readonly completed: boolean;
      } | null>;
      readonly completedSteps: ReadonlyArray<{
        readonly __typename?: "CompanyOnboardingStep";
        readonly stepType: CompanyOnboardingStepType;
        readonly mandatory: boolean;
        readonly completed: boolean;
      } | null>;
    } | null;
  } | null;
};

export type GetCompanyBasicDetailsQueryVariables = Exact<{
  [key: string]: never;
}>;

export type GetCompanyBasicDetailsQuery = {
  readonly __typename?: "Query";
  readonly getCompany?: {
    readonly __typename?: "Company";
    readonly id: string;
    readonly basicDetails?: {
      readonly __typename?: "CompanyBasicDetails";
      readonly name: string;
      readonly address?: string | null;
      readonly email: string;
      readonly website: string;
      readonly phoneNumber?: string | null;
      readonly industry?: string | null;
      readonly size?: EntitySize | null;
    } | null;
  } | null;
};

export type GetCompanyUsersQueryVariables = Exact<{ [key: string]: never }>;

export type GetCompanyUsersQuery = {
  readonly __typename?: "Query";
  readonly getCompany?: {
    readonly __typename?: "Company";
    readonly id: string;
    readonly users?: ReadonlyArray<{
      readonly __typename?: "CompanyUser";
      readonly id: string;
      readonly name: string;
      readonly email: string;
      readonly status: CompanyUserStatus;
      readonly roles: ReadonlyArray<CompanyUserRole | null>;
    } | null> | null;
  } | null;
};

export type GetCompanyUserQueryVariables = Exact<{ [key: string]: never }>;

export type GetCompanyUserQuery = {
  readonly __typename?: "Query";
  readonly getCompanyUser?: {
    readonly __typename?: "CompanyUser";
    readonly id: string;
    readonly name: string;
    readonly email: string;
    readonly status: CompanyUserStatus;
    readonly roles: ReadonlyArray<CompanyUserRole | null>;
  } | null;
};

export type GetAllCustomersQueryVariables = Exact<{
  status?: InputMaybe<CustomerStatus>;
  stage?: InputMaybe<CustomerStage>;
  customerType?: InputMaybe<CustomerType>;
}>;

export type GetAllCustomersQuery = {
  readonly __typename?: "Query";
  readonly getCustomers: ReadonlyArray<
    | {
        readonly __typename?: "CustomerBusiness";
        readonly id: string;
        readonly status: CustomerStatus;
        readonly stage: CustomerStage;
        readonly type: CustomerType;
        readonly basicDetails: {
          readonly __typename?: "BusinessCustomerBasicDetails";
          readonly legalName: string;
          readonly website: string;
          readonly size: EntitySize;
          readonly industry?: string | null;
          readonly referralSource?: string | null;
          readonly contactDetails: {
            readonly __typename?: "ContactDetails";
            readonly contactType: ContactType;
            readonly name: string;
            readonly title?: string | null;
            readonly email: string;
            readonly phoneNo?: string | null;
          };
        };
        readonly company: {
          readonly __typename?: "Company";
          readonly id: string;
        };
      }
    | {
        readonly __typename?: "CustomerIndividual";
        readonly id: string;
        readonly status: CustomerStatus;
        readonly stage: CustomerStage;
        readonly type: CustomerType;
        readonly basicDetails: {
          readonly __typename?: "IndividualCustomerBasicDetails";
          readonly referralSource?: string | null;
          readonly contactDetails: {
            readonly __typename?: "ContactDetails";
            readonly contactType: ContactType;
            readonly name: string;
            readonly title?: string | null;
            readonly email: string;
            readonly phoneNo?: string | null;
          };
        };
        readonly company: {
          readonly __typename?: "Company";
          readonly id: string;
        };
      }
  >;
};

export type GetCustomerByIdQueryVariables = Exact<{
  id: Scalars["ID"]["input"];
}>;

export type GetCustomerByIdQuery = {
  readonly __typename?: "Query";
  readonly getCustomer?:
    | {
        readonly __typename?: "CustomerBusiness";
        readonly id: string;
        readonly status: CustomerStatus;
        readonly stage: CustomerStage;
        readonly type: CustomerType;
        readonly basicDetails: {
          readonly __typename?: "BusinessCustomerBasicDetails";
          readonly legalName: string;
          readonly website: string;
          readonly size: EntitySize;
          readonly industry?: string | null;
          readonly referralSource?: string | null;
          readonly contactDetails: {
            readonly __typename?: "ContactDetails";
            readonly contactType: ContactType;
            readonly name: string;
            readonly title?: string | null;
            readonly email: string;
            readonly phoneNo?: string | null;
          };
        };
        readonly notes?: ReadonlyArray<{
          readonly __typename?: "Notes";
          readonly id?: string | null;
          readonly content: string;
          readonly tags: ReadonlyArray<string>;
        } | null> | null;
        readonly assignments: ReadonlyArray<{
          readonly __typename?: "CustomerAssignment";
          readonly accountManager?: {
            readonly __typename?: "CompanyUser";
            readonly id: string;
            readonly name: string;
            readonly email: string;
          } | null;
          readonly supportRepresentative?: {
            readonly __typename?: "CompanyUser";
            readonly id: string;
            readonly name: string;
            readonly email: string;
          } | null;
        } | null>;
        readonly customTags: ReadonlyArray<{
          readonly __typename?: "CustomTag";
          readonly id?: string | null;
          readonly key: string;
          readonly label: string;
          readonly value: string;
          readonly type: CustomTagType;
          readonly description?: string | null;
        } | null>;
        readonly company: {
          readonly __typename?: "Company";
          readonly id: string;
        };
      }
    | {
        readonly __typename?: "CustomerIndividual";
        readonly id: string;
        readonly status: CustomerStatus;
        readonly stage: CustomerStage;
        readonly type: CustomerType;
        readonly basicDetails: {
          readonly __typename?: "IndividualCustomerBasicDetails";
          readonly referralSource?: string | null;
          readonly contactDetails: {
            readonly __typename?: "ContactDetails";
            readonly contactType: ContactType;
            readonly name: string;
            readonly title?: string | null;
            readonly email: string;
            readonly phoneNo?: string | null;
          };
        };
        readonly notes?: ReadonlyArray<{
          readonly __typename?: "Notes";
          readonly id?: string | null;
          readonly content: string;
          readonly tags: ReadonlyArray<string>;
        } | null> | null;
        readonly assignments: ReadonlyArray<{
          readonly __typename?: "CustomerAssignment";
          readonly accountManager?: {
            readonly __typename?: "CompanyUser";
            readonly id: string;
            readonly name: string;
            readonly email: string;
          } | null;
          readonly supportRepresentative?: {
            readonly __typename?: "CompanyUser";
            readonly id: string;
            readonly name: string;
            readonly email: string;
          } | null;
        } | null>;
        readonly customTags: ReadonlyArray<{
          readonly __typename?: "CustomTag";
          readonly id?: string | null;
          readonly key: string;
          readonly label: string;
          readonly value: string;
          readonly type: CustomTagType;
          readonly description?: string | null;
        } | null>;
        readonly company: {
          readonly __typename?: "Company";
          readonly id: string;
        };
      }
    | null;
};

export type GetAllProductsQueryVariables = Exact<{
  status?: InputMaybe<ProductStatus>;
}>;

export type GetAllProductsQuery = {
  readonly __typename?: "Query";
  readonly masterProductsGet: ReadonlyArray<{
    readonly __typename?: "MasterProduct";
    readonly id: string;
    readonly name: string;
    readonly description?: string | null;
    readonly status: ProductStatus;
    readonly productCode?: string | null;
    readonly dimensions?: ReadonlyArray<{
      readonly __typename?: "Dimension";
      readonly key: string;
      readonly value: string;
    }> | null;
    readonly pricing: {
      readonly __typename?: "Pricing";
      readonly id: string;
      readonly chargePolicy: ChargePolicy;
      readonly costPrice: {
        readonly __typename?: "Amount";
        readonly value: number;
        readonly currency: Currency;
      };
      readonly listPrice: {
        readonly __typename?: "Amount";
        readonly value: number;
        readonly currency: Currency;
      };
      readonly sellingPrice: {
        readonly __typename?: "Amount";
        readonly value: number;
        readonly currency: Currency;
      };
      readonly unit: {
        readonly __typename?: "ProductUnit";
        readonly unit: number;
        readonly unitType: string;
      };
      readonly discount: ReadonlyArray<{
        readonly __typename?: "Discount";
        readonly id: string;
        readonly discountType: DiscountType;
        readonly discountValue: {
          readonly __typename?: "DiscountValue";
          readonly percentage: number;
          readonly value: {
            readonly __typename?: "Amount";
            readonly value: number;
            readonly currency: Currency;
          };
        };
      } | null>;
    };
  }>;
};

export type GetProductByIdQueryVariables = Exact<{
  id: Scalars["ID"]["input"];
}>;

export type GetProductByIdQuery = {
  readonly __typename?: "Query";
  readonly masterProductGetById: {
    readonly __typename?: "MasterProduct";
    readonly id: string;
    readonly name: string;
    readonly description?: string | null;
    readonly status: ProductStatus;
    readonly productCode?: string | null;
    readonly dimensions?: ReadonlyArray<{
      readonly __typename?: "Dimension";
      readonly key: string;
      readonly value: string;
    }> | null;
    readonly documents?: ReadonlyArray<{
      readonly __typename?: "Document";
      readonly id?: string | null;
      readonly file?: {
        readonly __typename?: "FileLink";
        readonly signedReadURL?: string | null;
        readonly signedWriteURL?: string | null;
      } | null;
    }> | null;
    readonly customTags?: ReadonlyArray<{
      readonly __typename?: "CustomTag";
      readonly id?: string | null;
      readonly key: string;
      readonly label: string;
      readonly value: string;
      readonly type: CustomTagType;
      readonly description?: string | null;
    }> | null;
    readonly pricing: {
      readonly __typename?: "Pricing";
      readonly id: string;
      readonly chargePolicy: ChargePolicy;
      readonly costPrice: {
        readonly __typename?: "Amount";
        readonly value: number;
        readonly currency: Currency;
      };
      readonly listPrice: {
        readonly __typename?: "Amount";
        readonly value: number;
        readonly currency: Currency;
      };
      readonly sellingPrice: {
        readonly __typename?: "Amount";
        readonly value: number;
        readonly currency: Currency;
      };
      readonly unit: {
        readonly __typename?: "ProductUnit";
        readonly unit: number;
        readonly unitType: string;
      };
      readonly discount: ReadonlyArray<{
        readonly __typename?: "Discount";
        readonly id: string;
        readonly discountType: DiscountType;
        readonly discountValue: {
          readonly __typename?: "DiscountValue";
          readonly percentage: number;
          readonly value: {
            readonly __typename?: "Amount";
            readonly value: number;
            readonly currency: Currency;
          };
        };
      } | null>;
    };
  };
};

export const CompanyUserCreateDocument = gql`
  mutation CompanyUserCreate($input: CompanyUserCreateInput!) {
    companyUserCreate(input: $input) {
      id
      name
      email
      status
      roles
    }
  }
`;
export type CompanyUserCreateMutationFn = Apollo.MutationFunction<
  CompanyUserCreateMutation,
  CompanyUserCreateMutationVariables
>;

/**
 * __useCompanyUserCreateMutation__
 *
 * To run a mutation, you first call `useCompanyUserCreateMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCompanyUserCreateMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [companyUserCreateMutation, { data, loading, error }] = useCompanyUserCreateMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCompanyUserCreateMutation(
  baseOptions?: ApolloReactHooks.MutationHookOptions<
    CompanyUserCreateMutation,
    CompanyUserCreateMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useMutation<
    CompanyUserCreateMutation,
    CompanyUserCreateMutationVariables
  >(CompanyUserCreateDocument, options);
}
export type CompanyUserCreateMutationHookResult = ReturnType<
  typeof useCompanyUserCreateMutation
>;
export type CompanyUserCreateMutationResult =
  Apollo.MutationResult<CompanyUserCreateMutation>;
export type CompanyUserCreateMutationOptions = Apollo.BaseMutationOptions<
  CompanyUserCreateMutation,
  CompanyUserCreateMutationVariables
>;
export const CompanyUserInviteDocument = gql`
  mutation CompanyUserInvite($companyUserId: ID!) {
    companyUserInvite(companyUserId: $companyUserId) {
      id
      name
      email
      status
      roles
    }
  }
`;
export type CompanyUserInviteMutationFn = Apollo.MutationFunction<
  CompanyUserInviteMutation,
  CompanyUserInviteMutationVariables
>;

/**
 * __useCompanyUserInviteMutation__
 *
 * To run a mutation, you first call `useCompanyUserInviteMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCompanyUserInviteMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [companyUserInviteMutation, { data, loading, error }] = useCompanyUserInviteMutation({
 *   variables: {
 *      companyUserId: // value for 'companyUserId'
 *   },
 * });
 */
export function useCompanyUserInviteMutation(
  baseOptions?: ApolloReactHooks.MutationHookOptions<
    CompanyUserInviteMutation,
    CompanyUserInviteMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useMutation<
    CompanyUserInviteMutation,
    CompanyUserInviteMutationVariables
  >(CompanyUserInviteDocument, options);
}
export type CompanyUserInviteMutationHookResult = ReturnType<
  typeof useCompanyUserInviteMutation
>;
export type CompanyUserInviteMutationResult =
  Apollo.MutationResult<CompanyUserInviteMutation>;
export type CompanyUserInviteMutationOptions = Apollo.BaseMutationOptions<
  CompanyUserInviteMutation,
  CompanyUserInviteMutationVariables
>;
export const CompanyUserDeactivateDocument = gql`
  mutation CompanyUserDeactivate($companyUserId: ID!) {
    companyUserDeactivate(companyUserId: $companyUserId) {
      success
      message
    }
  }
`;
export type CompanyUserDeactivateMutationFn = Apollo.MutationFunction<
  CompanyUserDeactivateMutation,
  CompanyUserDeactivateMutationVariables
>;

/**
 * __useCompanyUserDeactivateMutation__
 *
 * To run a mutation, you first call `useCompanyUserDeactivateMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCompanyUserDeactivateMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [companyUserDeactivateMutation, { data, loading, error }] = useCompanyUserDeactivateMutation({
 *   variables: {
 *      companyUserId: // value for 'companyUserId'
 *   },
 * });
 */
export function useCompanyUserDeactivateMutation(
  baseOptions?: ApolloReactHooks.MutationHookOptions<
    CompanyUserDeactivateMutation,
    CompanyUserDeactivateMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useMutation<
    CompanyUserDeactivateMutation,
    CompanyUserDeactivateMutationVariables
  >(CompanyUserDeactivateDocument, options);
}
export type CompanyUserDeactivateMutationHookResult = ReturnType<
  typeof useCompanyUserDeactivateMutation
>;
export type CompanyUserDeactivateMutationResult =
  Apollo.MutationResult<CompanyUserDeactivateMutation>;
export type CompanyUserDeactivateMutationOptions = Apollo.BaseMutationOptions<
  CompanyUserDeactivateMutation,
  CompanyUserDeactivateMutationVariables
>;
export const CompanyUserActivateDocument = gql`
  mutation CompanyUserActivate {
    companyUserActivate {
      id
      name
      email
      status
      roles
    }
  }
`;
export type CompanyUserActivateMutationFn = Apollo.MutationFunction<
  CompanyUserActivateMutation,
  CompanyUserActivateMutationVariables
>;

/**
 * __useCompanyUserActivateMutation__
 *
 * To run a mutation, you first call `useCompanyUserActivateMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCompanyUserActivateMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [companyUserActivateMutation, { data, loading, error }] = useCompanyUserActivateMutation({
 *   variables: {
 *   },
 * });
 */
export function useCompanyUserActivateMutation(
  baseOptions?: ApolloReactHooks.MutationHookOptions<
    CompanyUserActivateMutation,
    CompanyUserActivateMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useMutation<
    CompanyUserActivateMutation,
    CompanyUserActivateMutationVariables
  >(CompanyUserActivateDocument, options);
}
export type CompanyUserActivateMutationHookResult = ReturnType<
  typeof useCompanyUserActivateMutation
>;
export type CompanyUserActivateMutationResult =
  Apollo.MutationResult<CompanyUserActivateMutation>;
export type CompanyUserActivateMutationOptions = Apollo.BaseMutationOptions<
  CompanyUserActivateMutation,
  CompanyUserActivateMutationVariables
>;
export const CreateCompanyBasicDetailsDocument = gql`
  mutation CreateCompanyBasicDetails($input: CompanyCreateBasicDetailsInput!) {
    companyCreateBasicDetails(input: $input) {
      id
      basicDetails {
        name
        address
        phoneNumber
        email
        website
        industry
        size
      }
    }
  }
`;
export type CreateCompanyBasicDetailsMutationFn = Apollo.MutationFunction<
  CreateCompanyBasicDetailsMutation,
  CreateCompanyBasicDetailsMutationVariables
>;

/**
 * __useCreateCompanyBasicDetailsMutation__
 *
 * To run a mutation, you first call `useCreateCompanyBasicDetailsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateCompanyBasicDetailsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createCompanyBasicDetailsMutation, { data, loading, error }] = useCreateCompanyBasicDetailsMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCreateCompanyBasicDetailsMutation(
  baseOptions?: ApolloReactHooks.MutationHookOptions<
    CreateCompanyBasicDetailsMutation,
    CreateCompanyBasicDetailsMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useMutation<
    CreateCompanyBasicDetailsMutation,
    CreateCompanyBasicDetailsMutationVariables
  >(CreateCompanyBasicDetailsDocument, options);
}
export type CreateCompanyBasicDetailsMutationHookResult = ReturnType<
  typeof useCreateCompanyBasicDetailsMutation
>;
export type CreateCompanyBasicDetailsMutationResult =
  Apollo.MutationResult<CreateCompanyBasicDetailsMutation>;
export type CreateCompanyBasicDetailsMutationOptions =
  Apollo.BaseMutationOptions<
    CreateCompanyBasicDetailsMutation,
    CreateCompanyBasicDetailsMutationVariables
  >;
export const UpdateCompanyBasicDetailsDocument = gql`
  mutation UpdateCompanyBasicDetails($input: CompanyUpdateBasicDetailsInput!) {
    companyUpdateBasicDetails(input: $input) {
      id
      basicDetails {
        name
        address
        phoneNumber
        email
        website
        industry
        size
      }
    }
  }
`;
export type UpdateCompanyBasicDetailsMutationFn = Apollo.MutationFunction<
  UpdateCompanyBasicDetailsMutation,
  UpdateCompanyBasicDetailsMutationVariables
>;

/**
 * __useUpdateCompanyBasicDetailsMutation__
 *
 * To run a mutation, you first call `useUpdateCompanyBasicDetailsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateCompanyBasicDetailsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateCompanyBasicDetailsMutation, { data, loading, error }] = useUpdateCompanyBasicDetailsMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdateCompanyBasicDetailsMutation(
  baseOptions?: ApolloReactHooks.MutationHookOptions<
    UpdateCompanyBasicDetailsMutation,
    UpdateCompanyBasicDetailsMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useMutation<
    UpdateCompanyBasicDetailsMutation,
    UpdateCompanyBasicDetailsMutationVariables
  >(UpdateCompanyBasicDetailsDocument, options);
}
export type UpdateCompanyBasicDetailsMutationHookResult = ReturnType<
  typeof useUpdateCompanyBasicDetailsMutation
>;
export type UpdateCompanyBasicDetailsMutationResult =
  Apollo.MutationResult<UpdateCompanyBasicDetailsMutation>;
export type UpdateCompanyBasicDetailsMutationOptions =
  Apollo.BaseMutationOptions<
    UpdateCompanyBasicDetailsMutation,
    UpdateCompanyBasicDetailsMutationVariables
  >;
export const CustomerUpsertBasicDetailsDocument = gql`
  mutation CustomerUpsertBasicDetails(
    $input: CustomerUpsertBasicDetailsInput!
  ) {
    customerUpsertBasicDetails(input: $input) {
      id
    }
  }
`;
export type CustomerUpsertBasicDetailsMutationFn = Apollo.MutationFunction<
  CustomerUpsertBasicDetailsMutation,
  CustomerUpsertBasicDetailsMutationVariables
>;

/**
 * __useCustomerUpsertBasicDetailsMutation__
 *
 * To run a mutation, you first call `useCustomerUpsertBasicDetailsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCustomerUpsertBasicDetailsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [customerUpsertBasicDetailsMutation, { data, loading, error }] = useCustomerUpsertBasicDetailsMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCustomerUpsertBasicDetailsMutation(
  baseOptions?: ApolloReactHooks.MutationHookOptions<
    CustomerUpsertBasicDetailsMutation,
    CustomerUpsertBasicDetailsMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useMutation<
    CustomerUpsertBasicDetailsMutation,
    CustomerUpsertBasicDetailsMutationVariables
  >(CustomerUpsertBasicDetailsDocument, options);
}
export type CustomerUpsertBasicDetailsMutationHookResult = ReturnType<
  typeof useCustomerUpsertBasicDetailsMutation
>;
export type CustomerUpsertBasicDetailsMutationResult =
  Apollo.MutationResult<CustomerUpsertBasicDetailsMutation>;
export type CustomerUpsertBasicDetailsMutationOptions =
  Apollo.BaseMutationOptions<
    CustomerUpsertBasicDetailsMutation,
    CustomerUpsertBasicDetailsMutationVariables
  >;
export const CustomerUpsertAdditionalDetailsDocument = gql`
  mutation CustomerUpsertAdditionalDetails(
    $input: CustomerUpsertAdditionalDetailsInput!
  ) {
    customerUpsertAdditionalDetails(input: $input) {
      id
    }
  }
`;
export type CustomerUpsertAdditionalDetailsMutationFn = Apollo.MutationFunction<
  CustomerUpsertAdditionalDetailsMutation,
  CustomerUpsertAdditionalDetailsMutationVariables
>;

/**
 * __useCustomerUpsertAdditionalDetailsMutation__
 *
 * To run a mutation, you first call `useCustomerUpsertAdditionalDetailsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCustomerUpsertAdditionalDetailsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [customerUpsertAdditionalDetailsMutation, { data, loading, error }] = useCustomerUpsertAdditionalDetailsMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCustomerUpsertAdditionalDetailsMutation(
  baseOptions?: ApolloReactHooks.MutationHookOptions<
    CustomerUpsertAdditionalDetailsMutation,
    CustomerUpsertAdditionalDetailsMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useMutation<
    CustomerUpsertAdditionalDetailsMutation,
    CustomerUpsertAdditionalDetailsMutationVariables
  >(CustomerUpsertAdditionalDetailsDocument, options);
}
export type CustomerUpsertAdditionalDetailsMutationHookResult = ReturnType<
  typeof useCustomerUpsertAdditionalDetailsMutation
>;
export type CustomerUpsertAdditionalDetailsMutationResult =
  Apollo.MutationResult<CustomerUpsertAdditionalDetailsMutation>;
export type CustomerUpsertAdditionalDetailsMutationOptions =
  Apollo.BaseMutationOptions<
    CustomerUpsertAdditionalDetailsMutation,
    CustomerUpsertAdditionalDetailsMutationVariables
  >;
export const MasterProductUpsertDetailsDocument = gql`
  mutation MasterProductUpsertDetails($input: MasterProductUpsertInput!) {
    masterProductUpsertDetails(input: $input) {
      id
      pricing {
        id
      }
    }
  }
`;
export type MasterProductUpsertDetailsMutationFn = Apollo.MutationFunction<
  MasterProductUpsertDetailsMutation,
  MasterProductUpsertDetailsMutationVariables
>;

/**
 * __useMasterProductUpsertDetailsMutation__
 *
 * To run a mutation, you first call `useMasterProductUpsertDetailsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useMasterProductUpsertDetailsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [masterProductUpsertDetailsMutation, { data, loading, error }] = useMasterProductUpsertDetailsMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useMasterProductUpsertDetailsMutation(
  baseOptions?: ApolloReactHooks.MutationHookOptions<
    MasterProductUpsertDetailsMutation,
    MasterProductUpsertDetailsMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useMutation<
    MasterProductUpsertDetailsMutation,
    MasterProductUpsertDetailsMutationVariables
  >(MasterProductUpsertDetailsDocument, options);
}
export type MasterProductUpsertDetailsMutationHookResult = ReturnType<
  typeof useMasterProductUpsertDetailsMutation
>;
export type MasterProductUpsertDetailsMutationResult =
  Apollo.MutationResult<MasterProductUpsertDetailsMutation>;
export type MasterProductUpsertDetailsMutationOptions =
  Apollo.BaseMutationOptions<
    MasterProductUpsertDetailsMutation,
    MasterProductUpsertDetailsMutationVariables
  >;
export const MasterProductUpsertAdditionalDetailsDocument = gql`
  mutation MasterProductUpsertAdditionalDetails(
    $input: MasterProductUpsertAdditionalDetailsInput!
  ) {
    masterProductUpsertAdditionalDetails(input: $input) {
      id
      pricing {
        id
      }
    }
  }
`;
export type MasterProductUpsertAdditionalDetailsMutationFn =
  Apollo.MutationFunction<
    MasterProductUpsertAdditionalDetailsMutation,
    MasterProductUpsertAdditionalDetailsMutationVariables
  >;

/**
 * __useMasterProductUpsertAdditionalDetailsMutation__
 *
 * To run a mutation, you first call `useMasterProductUpsertAdditionalDetailsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useMasterProductUpsertAdditionalDetailsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [masterProductUpsertAdditionalDetailsMutation, { data, loading, error }] = useMasterProductUpsertAdditionalDetailsMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useMasterProductUpsertAdditionalDetailsMutation(
  baseOptions?: ApolloReactHooks.MutationHookOptions<
    MasterProductUpsertAdditionalDetailsMutation,
    MasterProductUpsertAdditionalDetailsMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useMutation<
    MasterProductUpsertAdditionalDetailsMutation,
    MasterProductUpsertAdditionalDetailsMutationVariables
  >(MasterProductUpsertAdditionalDetailsDocument, options);
}
export type MasterProductUpsertAdditionalDetailsMutationHookResult = ReturnType<
  typeof useMasterProductUpsertAdditionalDetailsMutation
>;
export type MasterProductUpsertAdditionalDetailsMutationResult =
  Apollo.MutationResult<MasterProductUpsertAdditionalDetailsMutation>;
export type MasterProductUpsertAdditionalDetailsMutationOptions =
  Apollo.BaseMutationOptions<
    MasterProductUpsertAdditionalDetailsMutation,
    MasterProductUpsertAdditionalDetailsMutationVariables
  >;
export const ProposalProductUpsertDocument = gql`
  mutation ProposalProductUpsert($input: QuoteUpsertInput!) {
    quoteUpsert(input: $input) {
      id
      status
    }
  }
`;
export type ProposalProductUpsertMutationFn = Apollo.MutationFunction<
  ProposalProductUpsertMutation,
  ProposalProductUpsertMutationVariables
>;

/**
 * __useProposalProductUpsertMutation__
 *
 * To run a mutation, you first call `useProposalProductUpsertMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useProposalProductUpsertMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [proposalProductUpsertMutation, { data, loading, error }] = useProposalProductUpsertMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useProposalProductUpsertMutation(
  baseOptions?: ApolloReactHooks.MutationHookOptions<
    ProposalProductUpsertMutation,
    ProposalProductUpsertMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useMutation<
    ProposalProductUpsertMutation,
    ProposalProductUpsertMutationVariables
  >(ProposalProductUpsertDocument, options);
}
export type ProposalProductUpsertMutationHookResult = ReturnType<
  typeof useProposalProductUpsertMutation
>;
export type ProposalProductUpsertMutationResult =
  Apollo.MutationResult<ProposalProductUpsertMutation>;
export type ProposalProductUpsertMutationOptions = Apollo.BaseMutationOptions<
  ProposalProductUpsertMutation,
  ProposalProductUpsertMutationVariables
>;
export const OkDocument = gql`
  query Ok {
    ok
  }
`;

/**
 * __useOkQuery__
 *
 * To run a query within a React component, call `useOkQuery` and pass it any options that fit your needs.
 * When your component renders, `useOkQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useOkQuery({
 *   variables: {
 *   },
 * });
 */
export function useOkQuery(
  baseOptions?: ApolloReactHooks.QueryHookOptions<OkQuery, OkQueryVariables>,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useQuery<OkQuery, OkQueryVariables>(
    OkDocument,
    options,
  );
}
export function useOkLazyQuery(
  baseOptions?: ApolloReactHooks.LazyQueryHookOptions<
    OkQuery,
    OkQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useLazyQuery<OkQuery, OkQueryVariables>(
    OkDocument,
    options,
  );
}
export function useOkSuspenseQuery(
  baseOptions?:
    | ApolloReactHooks.SkipToken
    | ApolloReactHooks.SuspenseQueryHookOptions<OkQuery, OkQueryVariables>,
) {
  const options =
    baseOptions === ApolloReactHooks.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useSuspenseQuery<OkQuery, OkQueryVariables>(
    OkDocument,
    options,
  );
}
export type OkQueryHookResult = ReturnType<typeof useOkQuery>;
export type OkLazyQueryHookResult = ReturnType<typeof useOkLazyQuery>;
export type OkSuspenseQueryHookResult = ReturnType<typeof useOkSuspenseQuery>;
export type OkQueryResult = Apollo.QueryResult<OkQuery, OkQueryVariables>;
export function refetchOkQuery(variables?: OkQueryVariables) {
  return { query: OkDocument, variables: variables };
}
export const GetCompanyDocument = gql`
  query GetCompany {
    getCompany {
      id
      name
      status
      onboarding {
        id
        pendingSteps {
          stepType
          mandatory
          completed
        }
        completedSteps {
          stepType
          mandatory
          completed
        }
      }
    }
  }
`;

/**
 * __useGetCompanyQuery__
 *
 * To run a query within a React component, call `useGetCompanyQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetCompanyQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetCompanyQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetCompanyQuery(
  baseOptions?: ApolloReactHooks.QueryHookOptions<
    GetCompanyQuery,
    GetCompanyQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useQuery<GetCompanyQuery, GetCompanyQueryVariables>(
    GetCompanyDocument,
    options,
  );
}
export function useGetCompanyLazyQuery(
  baseOptions?: ApolloReactHooks.LazyQueryHookOptions<
    GetCompanyQuery,
    GetCompanyQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useLazyQuery<
    GetCompanyQuery,
    GetCompanyQueryVariables
  >(GetCompanyDocument, options);
}
export function useGetCompanySuspenseQuery(
  baseOptions?:
    | ApolloReactHooks.SkipToken
    | ApolloReactHooks.SuspenseQueryHookOptions<
        GetCompanyQuery,
        GetCompanyQueryVariables
      >,
) {
  const options =
    baseOptions === ApolloReactHooks.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useSuspenseQuery<
    GetCompanyQuery,
    GetCompanyQueryVariables
  >(GetCompanyDocument, options);
}
export type GetCompanyQueryHookResult = ReturnType<typeof useGetCompanyQuery>;
export type GetCompanyLazyQueryHookResult = ReturnType<
  typeof useGetCompanyLazyQuery
>;
export type GetCompanySuspenseQueryHookResult = ReturnType<
  typeof useGetCompanySuspenseQuery
>;
export type GetCompanyQueryResult = Apollo.QueryResult<
  GetCompanyQuery,
  GetCompanyQueryVariables
>;
export function refetchGetCompanyQuery(variables?: GetCompanyQueryVariables) {
  return { query: GetCompanyDocument, variables: variables };
}
export const GetCompanyBasicDetailsDocument = gql`
  query GetCompanyBasicDetails {
    getCompany {
      id
      basicDetails {
        name
        address
        email
        website
        phoneNumber
        industry
        size
      }
    }
  }
`;

/**
 * __useGetCompanyBasicDetailsQuery__
 *
 * To run a query within a React component, call `useGetCompanyBasicDetailsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetCompanyBasicDetailsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetCompanyBasicDetailsQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetCompanyBasicDetailsQuery(
  baseOptions?: ApolloReactHooks.QueryHookOptions<
    GetCompanyBasicDetailsQuery,
    GetCompanyBasicDetailsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useQuery<
    GetCompanyBasicDetailsQuery,
    GetCompanyBasicDetailsQueryVariables
  >(GetCompanyBasicDetailsDocument, options);
}
export function useGetCompanyBasicDetailsLazyQuery(
  baseOptions?: ApolloReactHooks.LazyQueryHookOptions<
    GetCompanyBasicDetailsQuery,
    GetCompanyBasicDetailsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useLazyQuery<
    GetCompanyBasicDetailsQuery,
    GetCompanyBasicDetailsQueryVariables
  >(GetCompanyBasicDetailsDocument, options);
}
export function useGetCompanyBasicDetailsSuspenseQuery(
  baseOptions?:
    | ApolloReactHooks.SkipToken
    | ApolloReactHooks.SuspenseQueryHookOptions<
        GetCompanyBasicDetailsQuery,
        GetCompanyBasicDetailsQueryVariables
      >,
) {
  const options =
    baseOptions === ApolloReactHooks.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useSuspenseQuery<
    GetCompanyBasicDetailsQuery,
    GetCompanyBasicDetailsQueryVariables
  >(GetCompanyBasicDetailsDocument, options);
}
export type GetCompanyBasicDetailsQueryHookResult = ReturnType<
  typeof useGetCompanyBasicDetailsQuery
>;
export type GetCompanyBasicDetailsLazyQueryHookResult = ReturnType<
  typeof useGetCompanyBasicDetailsLazyQuery
>;
export type GetCompanyBasicDetailsSuspenseQueryHookResult = ReturnType<
  typeof useGetCompanyBasicDetailsSuspenseQuery
>;
export type GetCompanyBasicDetailsQueryResult = Apollo.QueryResult<
  GetCompanyBasicDetailsQuery,
  GetCompanyBasicDetailsQueryVariables
>;
export function refetchGetCompanyBasicDetailsQuery(
  variables?: GetCompanyBasicDetailsQueryVariables,
) {
  return { query: GetCompanyBasicDetailsDocument, variables: variables };
}
export const GetCompanyUsersDocument = gql`
  query GetCompanyUsers {
    getCompany {
      id
      users {
        id
        name
        email
        status
        roles
      }
    }
  }
`;

/**
 * __useGetCompanyUsersQuery__
 *
 * To run a query within a React component, call `useGetCompanyUsersQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetCompanyUsersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetCompanyUsersQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetCompanyUsersQuery(
  baseOptions?: ApolloReactHooks.QueryHookOptions<
    GetCompanyUsersQuery,
    GetCompanyUsersQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useQuery<
    GetCompanyUsersQuery,
    GetCompanyUsersQueryVariables
  >(GetCompanyUsersDocument, options);
}
export function useGetCompanyUsersLazyQuery(
  baseOptions?: ApolloReactHooks.LazyQueryHookOptions<
    GetCompanyUsersQuery,
    GetCompanyUsersQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useLazyQuery<
    GetCompanyUsersQuery,
    GetCompanyUsersQueryVariables
  >(GetCompanyUsersDocument, options);
}
export function useGetCompanyUsersSuspenseQuery(
  baseOptions?:
    | ApolloReactHooks.SkipToken
    | ApolloReactHooks.SuspenseQueryHookOptions<
        GetCompanyUsersQuery,
        GetCompanyUsersQueryVariables
      >,
) {
  const options =
    baseOptions === ApolloReactHooks.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useSuspenseQuery<
    GetCompanyUsersQuery,
    GetCompanyUsersQueryVariables
  >(GetCompanyUsersDocument, options);
}
export type GetCompanyUsersQueryHookResult = ReturnType<
  typeof useGetCompanyUsersQuery
>;
export type GetCompanyUsersLazyQueryHookResult = ReturnType<
  typeof useGetCompanyUsersLazyQuery
>;
export type GetCompanyUsersSuspenseQueryHookResult = ReturnType<
  typeof useGetCompanyUsersSuspenseQuery
>;
export type GetCompanyUsersQueryResult = Apollo.QueryResult<
  GetCompanyUsersQuery,
  GetCompanyUsersQueryVariables
>;
export function refetchGetCompanyUsersQuery(
  variables?: GetCompanyUsersQueryVariables,
) {
  return { query: GetCompanyUsersDocument, variables: variables };
}
export const GetCompanyUserDocument = gql`
  query GetCompanyUser {
    getCompanyUser {
      id
      name
      email
      status
      roles
    }
  }
`;

/**
 * __useGetCompanyUserQuery__
 *
 * To run a query within a React component, call `useGetCompanyUserQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetCompanyUserQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetCompanyUserQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetCompanyUserQuery(
  baseOptions?: ApolloReactHooks.QueryHookOptions<
    GetCompanyUserQuery,
    GetCompanyUserQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useQuery<
    GetCompanyUserQuery,
    GetCompanyUserQueryVariables
  >(GetCompanyUserDocument, options);
}
export function useGetCompanyUserLazyQuery(
  baseOptions?: ApolloReactHooks.LazyQueryHookOptions<
    GetCompanyUserQuery,
    GetCompanyUserQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useLazyQuery<
    GetCompanyUserQuery,
    GetCompanyUserQueryVariables
  >(GetCompanyUserDocument, options);
}
export function useGetCompanyUserSuspenseQuery(
  baseOptions?:
    | ApolloReactHooks.SkipToken
    | ApolloReactHooks.SuspenseQueryHookOptions<
        GetCompanyUserQuery,
        GetCompanyUserQueryVariables
      >,
) {
  const options =
    baseOptions === ApolloReactHooks.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useSuspenseQuery<
    GetCompanyUserQuery,
    GetCompanyUserQueryVariables
  >(GetCompanyUserDocument, options);
}
export type GetCompanyUserQueryHookResult = ReturnType<
  typeof useGetCompanyUserQuery
>;
export type GetCompanyUserLazyQueryHookResult = ReturnType<
  typeof useGetCompanyUserLazyQuery
>;
export type GetCompanyUserSuspenseQueryHookResult = ReturnType<
  typeof useGetCompanyUserSuspenseQuery
>;
export type GetCompanyUserQueryResult = Apollo.QueryResult<
  GetCompanyUserQuery,
  GetCompanyUserQueryVariables
>;
export function refetchGetCompanyUserQuery(
  variables?: GetCompanyUserQueryVariables,
) {
  return { query: GetCompanyUserDocument, variables: variables };
}
export const GetAllCustomersDocument = gql`
  query GetAllCustomers(
    $status: CustomerStatus
    $stage: CustomerStage
    $customerType: CustomerType
  ) {
    getCustomers(
      filters: { status: $status, stage: $stage, customerType: $customerType }
    ) {
      id
      company {
        id
      }
      status
      stage
      type
      ... on CustomerIndividual {
        basicDetails {
          contactDetails {
            contactType
            name
            title
            email
            phoneNo
          }
          referralSource
        }
      }
      ... on CustomerBusiness {
        basicDetails {
          legalName
          website
          size
          industry
          referralSource
          contactDetails {
            contactType
            name
            title
            email
            phoneNo
          }
        }
      }
    }
  }
`;

/**
 * __useGetAllCustomersQuery__
 *
 * To run a query within a React component, call `useGetAllCustomersQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetAllCustomersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetAllCustomersQuery({
 *   variables: {
 *      status: // value for 'status'
 *      stage: // value for 'stage'
 *      customerType: // value for 'customerType'
 *   },
 * });
 */
export function useGetAllCustomersQuery(
  baseOptions?: ApolloReactHooks.QueryHookOptions<
    GetAllCustomersQuery,
    GetAllCustomersQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useQuery<
    GetAllCustomersQuery,
    GetAllCustomersQueryVariables
  >(GetAllCustomersDocument, options);
}
export function useGetAllCustomersLazyQuery(
  baseOptions?: ApolloReactHooks.LazyQueryHookOptions<
    GetAllCustomersQuery,
    GetAllCustomersQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useLazyQuery<
    GetAllCustomersQuery,
    GetAllCustomersQueryVariables
  >(GetAllCustomersDocument, options);
}
export function useGetAllCustomersSuspenseQuery(
  baseOptions?:
    | ApolloReactHooks.SkipToken
    | ApolloReactHooks.SuspenseQueryHookOptions<
        GetAllCustomersQuery,
        GetAllCustomersQueryVariables
      >,
) {
  const options =
    baseOptions === ApolloReactHooks.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useSuspenseQuery<
    GetAllCustomersQuery,
    GetAllCustomersQueryVariables
  >(GetAllCustomersDocument, options);
}
export type GetAllCustomersQueryHookResult = ReturnType<
  typeof useGetAllCustomersQuery
>;
export type GetAllCustomersLazyQueryHookResult = ReturnType<
  typeof useGetAllCustomersLazyQuery
>;
export type GetAllCustomersSuspenseQueryHookResult = ReturnType<
  typeof useGetAllCustomersSuspenseQuery
>;
export type GetAllCustomersQueryResult = Apollo.QueryResult<
  GetAllCustomersQuery,
  GetAllCustomersQueryVariables
>;
export function refetchGetAllCustomersQuery(
  variables?: GetAllCustomersQueryVariables,
) {
  return { query: GetAllCustomersDocument, variables: variables };
}
export const GetCustomerByIdDocument = gql`
  query GetCustomerById($id: ID!) {
    getCustomer(id: $id) {
      id
      status
      stage
      type
      company {
        id
      }
      ... on CustomerIndividual {
        basicDetails {
          contactDetails {
            contactType
            name
            title
            email
            phoneNo
          }
          referralSource
        }
        notes {
          id
          content
          tags
        }
        assignments {
          accountManager {
            id
            name
            email
          }
          supportRepresentative {
            id
            name
            email
          }
        }
        customTags {
          id
          key
          label
          value
          type
          description
        }
      }
      ... on CustomerBusiness {
        basicDetails {
          legalName
          website
          size
          industry
          referralSource
          contactDetails {
            contactType
            name
            title
            email
            phoneNo
          }
        }
        notes {
          id
          content
          tags
        }
        assignments {
          accountManager {
            id
            name
            email
          }
          supportRepresentative {
            id
            name
            email
          }
        }
        customTags {
          id
          key
          label
          value
          type
          description
        }
      }
    }
  }
`;

/**
 * __useGetCustomerByIdQuery__
 *
 * To run a query within a React component, call `useGetCustomerByIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetCustomerByIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetCustomerByIdQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useGetCustomerByIdQuery(
  baseOptions: ApolloReactHooks.QueryHookOptions<
    GetCustomerByIdQuery,
    GetCustomerByIdQueryVariables
  > &
    (
      | { variables: GetCustomerByIdQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useQuery<
    GetCustomerByIdQuery,
    GetCustomerByIdQueryVariables
  >(GetCustomerByIdDocument, options);
}
export function useGetCustomerByIdLazyQuery(
  baseOptions?: ApolloReactHooks.LazyQueryHookOptions<
    GetCustomerByIdQuery,
    GetCustomerByIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useLazyQuery<
    GetCustomerByIdQuery,
    GetCustomerByIdQueryVariables
  >(GetCustomerByIdDocument, options);
}
export function useGetCustomerByIdSuspenseQuery(
  baseOptions?:
    | ApolloReactHooks.SkipToken
    | ApolloReactHooks.SuspenseQueryHookOptions<
        GetCustomerByIdQuery,
        GetCustomerByIdQueryVariables
      >,
) {
  const options =
    baseOptions === ApolloReactHooks.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useSuspenseQuery<
    GetCustomerByIdQuery,
    GetCustomerByIdQueryVariables
  >(GetCustomerByIdDocument, options);
}
export type GetCustomerByIdQueryHookResult = ReturnType<
  typeof useGetCustomerByIdQuery
>;
export type GetCustomerByIdLazyQueryHookResult = ReturnType<
  typeof useGetCustomerByIdLazyQuery
>;
export type GetCustomerByIdSuspenseQueryHookResult = ReturnType<
  typeof useGetCustomerByIdSuspenseQuery
>;
export type GetCustomerByIdQueryResult = Apollo.QueryResult<
  GetCustomerByIdQuery,
  GetCustomerByIdQueryVariables
>;
export function refetchGetCustomerByIdQuery(
  variables: GetCustomerByIdQueryVariables,
) {
  return { query: GetCustomerByIdDocument, variables: variables };
}
export const GetAllProductsDocument = gql`
  query GetAllProducts($status: ProductStatus) {
    masterProductsGet(filters: { status: $status }) {
      id
      name
      description
      status
      productCode
      dimensions {
        key
        value
      }
      pricing {
        id
        chargePolicy
        costPrice {
          value
          currency
        }
        listPrice {
          value
          currency
        }
        sellingPrice {
          value
          currency
        }
        unit {
          unit
          unitType
        }
        discount {
          id
          discountType
          discountValue {
            value {
              value
              currency
            }
            percentage
          }
        }
      }
    }
  }
`;

/**
 * __useGetAllProductsQuery__
 *
 * To run a query within a React component, call `useGetAllProductsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetAllProductsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetAllProductsQuery({
 *   variables: {
 *      status: // value for 'status'
 *   },
 * });
 */
export function useGetAllProductsQuery(
  baseOptions?: ApolloReactHooks.QueryHookOptions<
    GetAllProductsQuery,
    GetAllProductsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useQuery<
    GetAllProductsQuery,
    GetAllProductsQueryVariables
  >(GetAllProductsDocument, options);
}
export function useGetAllProductsLazyQuery(
  baseOptions?: ApolloReactHooks.LazyQueryHookOptions<
    GetAllProductsQuery,
    GetAllProductsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useLazyQuery<
    GetAllProductsQuery,
    GetAllProductsQueryVariables
  >(GetAllProductsDocument, options);
}
export function useGetAllProductsSuspenseQuery(
  baseOptions?:
    | ApolloReactHooks.SkipToken
    | ApolloReactHooks.SuspenseQueryHookOptions<
        GetAllProductsQuery,
        GetAllProductsQueryVariables
      >,
) {
  const options =
    baseOptions === ApolloReactHooks.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useSuspenseQuery<
    GetAllProductsQuery,
    GetAllProductsQueryVariables
  >(GetAllProductsDocument, options);
}
export type GetAllProductsQueryHookResult = ReturnType<
  typeof useGetAllProductsQuery
>;
export type GetAllProductsLazyQueryHookResult = ReturnType<
  typeof useGetAllProductsLazyQuery
>;
export type GetAllProductsSuspenseQueryHookResult = ReturnType<
  typeof useGetAllProductsSuspenseQuery
>;
export type GetAllProductsQueryResult = Apollo.QueryResult<
  GetAllProductsQuery,
  GetAllProductsQueryVariables
>;
export function refetchGetAllProductsQuery(
  variables?: GetAllProductsQueryVariables,
) {
  return { query: GetAllProductsDocument, variables: variables };
}
export const GetProductByIdDocument = gql`
  query GetProductById($id: ID!) {
    masterProductGetById(id: $id) {
      id
      name
      description
      status
      productCode
      dimensions {
        key
        value
      }
      documents {
        id
        file {
          signedReadURL
          signedWriteURL
        }
      }
      customTags {
        id
        key
        label
        value
        type
        description
      }
      pricing {
        id
        chargePolicy
        costPrice {
          value
          currency
        }
        listPrice {
          value
          currency
        }
        sellingPrice {
          value
          currency
        }
        unit {
          unit
          unitType
        }
        discount {
          id
          discountType
          discountValue {
            value {
              value
              currency
            }
            percentage
          }
        }
      }
    }
  }
`;

/**
 * __useGetProductByIdQuery__
 *
 * To run a query within a React component, call `useGetProductByIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetProductByIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetProductByIdQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useGetProductByIdQuery(
  baseOptions: ApolloReactHooks.QueryHookOptions<
    GetProductByIdQuery,
    GetProductByIdQueryVariables
  > &
    (
      | { variables: GetProductByIdQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useQuery<
    GetProductByIdQuery,
    GetProductByIdQueryVariables
  >(GetProductByIdDocument, options);
}
export function useGetProductByIdLazyQuery(
  baseOptions?: ApolloReactHooks.LazyQueryHookOptions<
    GetProductByIdQuery,
    GetProductByIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useLazyQuery<
    GetProductByIdQuery,
    GetProductByIdQueryVariables
  >(GetProductByIdDocument, options);
}
export function useGetProductByIdSuspenseQuery(
  baseOptions?:
    | ApolloReactHooks.SkipToken
    | ApolloReactHooks.SuspenseQueryHookOptions<
        GetProductByIdQuery,
        GetProductByIdQueryVariables
      >,
) {
  const options =
    baseOptions === ApolloReactHooks.skipToken
      ? baseOptions
      : { ...defaultOptions, ...baseOptions };
  return ApolloReactHooks.useSuspenseQuery<
    GetProductByIdQuery,
    GetProductByIdQueryVariables
  >(GetProductByIdDocument, options);
}
export type GetProductByIdQueryHookResult = ReturnType<
  typeof useGetProductByIdQuery
>;
export type GetProductByIdLazyQueryHookResult = ReturnType<
  typeof useGetProductByIdLazyQuery
>;
export type GetProductByIdSuspenseQueryHookResult = ReturnType<
  typeof useGetProductByIdSuspenseQuery
>;
export type GetProductByIdQueryResult = Apollo.QueryResult<
  GetProductByIdQuery,
  GetProductByIdQueryVariables
>;
export function refetchGetProductByIdQuery(
  variables: GetProductByIdQueryVariables,
) {
  return { query: GetProductByIdDocument, variables: variables };
}
