/* eslint-disable */
import { TypedDocumentNode as DocumentNode } from "@graphql-typed-document-node/core";
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = {
  [K in keyof T]: T[K];
};
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]?: Maybe<T[SubKey]>;
};
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]: Maybe<T[SubKey]>;
};
export type MakeEmpty<
  T extends { [key: string]: unknown },
  K extends keyof T,
> = { [_ in K]?: never };
export type Incremental<T> =
  | T
  | {
      [P in keyof T]?: P extends " $fragmentName" | "__typename" ? T[P] : never;
    };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string };
  String: { input: string; output: string };
  Boolean: { input: boolean; output: boolean };
  Int: { input: number; output: number };
  Float: { input: number; output: number };
  Date: { input: any; output: any };
  DateTime: { input: any; output: any };
  _FieldSet: { input: any; output: any };
};

export type Amount = {
  __typename?: "Amount";
  currency: Currency;
  value: Scalars["Float"]["output"];
};

export type AmountInput = {
  currency: Currency;
  value: Scalars["Float"]["input"];
};

/**  Company user ids */
export type AssignmentInput = {
  accountManager?: InputMaybe<Scalars["ID"]["input"]>;
  supportRepresentative?: InputMaybe<Scalars["ID"]["input"]>;
};

export type BusinessCustomerBasicDetails = {
  __typename?: "BusinessCustomerBasicDetails";
  address?: Maybe<Scalars["String"]["output"]>;
  contactDetails: ContactDetails;
  industry?: Maybe<Scalars["String"]["output"]>;
  legalName: Scalars["String"]["output"];
  referralSource?: Maybe<Scalars["String"]["output"]>;
  size: EntitySize;
  website: Scalars["String"]["output"];
};

export type BusinessCustomerBasicDetailsInput = {
  address?: InputMaybe<Scalars["String"]["input"]>;
  contactPersonDetails: ContactPersonDetailsInput;
  industry?: InputMaybe<Scalars["String"]["input"]>;
  legalName: Scalars["String"]["input"];
  referralSource?: InputMaybe<Scalars["String"]["input"]>;
  size: EntitySize;
  website: Scalars["String"]["input"];
};

export enum ChargePolicy {
  Unit = "UNIT",
}

export type Company = {
  __typename?: "Company";
  basicDetails?: Maybe<CompanyBasicDetails>;
  id: Scalars["ID"]["output"];
  name?: Maybe<Scalars["String"]["output"]>;
  onboarding?: Maybe<CompanyOnboarding>;
  status?: Maybe<CompanyStatus>;
  users?: Maybe<Array<Maybe<CompanyUser>>>;
};

export type CompanyBasicDetails = {
  __typename?: "CompanyBasicDetails";
  address?: Maybe<Scalars["String"]["output"]>;
  email: Scalars["String"]["output"];
  industry?: Maybe<Scalars["String"]["output"]>;
  name: Scalars["String"]["output"];
  phoneNumber?: Maybe<Scalars["String"]["output"]>;
  size?: Maybe<EntitySize>;
  website: Scalars["String"]["output"];
};

export type CompanyCreateBasicDetailsInput = {
  address?: InputMaybe<Scalars["String"]["input"]>;
  email: Scalars["String"]["input"];
  industry?: InputMaybe<Scalars["String"]["input"]>;
  name: Scalars["String"]["input"];
  phoneNumber?: InputMaybe<Scalars["String"]["input"]>;
  size?: InputMaybe<EntitySize>;
  website: Scalars["String"]["input"];
};

export type CompanyEmail = Email & {
  __typename?: "CompanyEmail";
  attachments?: Maybe<Array<Scalars["String"]["output"]>>;
  bcc?: Maybe<Array<Scalars["String"]["output"]>>;
  /**  optional as the template can support this as well */
  body?: Maybe<Scalars["String"]["output"]>;
  cc?: Maybe<Array<Scalars["String"]["output"]>>;
  /**  optional as the template can support this as well */
  recipients: Array<Scalars["String"]["output"]>;
  subject?: Maybe<Scalars["String"]["output"]>;
  template: CompanyEmailTemplate;
  type: EmailType;
};

export type CompanyEmailTemplate = EmailTemplate & {
  __typename?: "CompanyEmailTemplate";
  body: Scalars["String"]["output"];
  fromName?: Maybe<Scalars["String"]["output"]>;
  subject: Scalars["String"]["output"];
  type: CompanyEmailTemplateType;
};

export enum CompanyEmailTemplateType {
  CompanyUserInvitationEmail = "COMPANY_USER_INVITATION_EMAIL",
  CompanyWelcomeEmail = "COMPANY_WELCOME_EMAIL",
}

export type CompanyOnboarding = {
  __typename?: "CompanyOnboarding";
  completedSteps: Array<Maybe<CompanyOnboardingStep>>;
  id: Scalars["ID"]["output"];
  pendingSteps: Array<Maybe<CompanyOnboardingStep>>;
};

export type CompanyOnboardingStep = {
  __typename?: "CompanyOnboardingStep";
  completed: Scalars["Boolean"]["output"];
  mandatory: Scalars["Boolean"]["output"];
  stepType: CompanyOnboardingStepType;
};

export enum CompanyOnboardingStepType {
  AccountingDetails = "ACCOUNTING_DETAILS",
  BasicDetails = "BASIC_DETAILS",
  CatalogDetails = "CATALOG_DETAILS",
  CompanyUsersDetails = "COMPANY_USERS_DETAILS",
}

export enum CompanyStatus {
  Active = "ACTIVE",
  Created = "CREATED",
  Inactive = "INACTIVE",
  Onboarding = "ONBOARDING",
}

export type CompanyUpdateBasicDetailsInput = {
  address?: InputMaybe<Scalars["String"]["input"]>;
  companyId: Scalars["ID"]["input"];
  industry?: InputMaybe<Scalars["String"]["input"]>;
  phoneNumber?: InputMaybe<Scalars["String"]["input"]>;
  size?: InputMaybe<EntitySize>;
  website: Scalars["String"]["input"];
};

export type CompanyUser = {
  __typename?: "CompanyUser";
  company: Company;
  email: Scalars["String"]["output"];
  id: Scalars["ID"]["output"];
  name: Scalars["String"]["output"];
  phoneNumber?: Maybe<Scalars["String"]["output"]>;
  roles: Array<Maybe<CompanyUserRole>>;
  status: CompanyUserStatus;
};

/**  input financial settings */
export type CompanyUserCreateInput = {
  companyId: Scalars["ID"]["input"];
  email: Scalars["String"]["input"];
  name: Scalars["String"]["input"];
  roles: Array<CompanyUserRole>;
};

export enum CompanyUserRole {
  AccountExecutive = "ACCOUNT_EXECUTIVE",
  Admin = "ADMIN",
  CharteredAccountant = "CHARTERED_ACCOUNTANT",
  CustomerSuccessManager = "CUSTOMER_SUCCESS_MANAGER",
}

export enum CompanyUserStatus {
  Active = "ACTIVE",
  Created = "CREATED",
  Inactive = "INACTIVE",
  Invited = "INVITED",
}

export type ContactDetails = {
  __typename?: "ContactDetails";
  contactType: ContactType;
  email: Scalars["String"]["output"];
  name: Scalars["String"]["output"];
  phoneNo?: Maybe<Scalars["String"]["output"]>;
  title?: Maybe<Scalars["String"]["output"]>;
};

export type ContactPersonDetailsInput = {
  contactType: ContactType;
  email: Scalars["String"]["input"];
  name: Scalars["String"]["input"];
  phoneNo?: InputMaybe<Scalars["String"]["input"]>;
  title?: InputMaybe<Scalars["String"]["input"]>;
};

export enum ContactType {
  Business = "BUSINESS",
  Person = "PERSON",
}

export enum Currency {
  Aed = "AED",
  Inr = "INR",
  Usd = "USD",
}

export type CustomTag = {
  __typename?: "CustomTag";
  description?: Maybe<Scalars["String"]["output"]>;
  id?: Maybe<Scalars["ID"]["output"]>;
  /**  to support client dto transformations */
  key: Scalars["String"]["output"];
  label: Scalars["String"]["output"];
  type: CustomTagType;
  value: Scalars["String"]["output"];
};

export type CustomTagInput = {
  description?: InputMaybe<Scalars["String"]["input"]>;
  id?: InputMaybe<Scalars["ID"]["input"]>;
  key: Scalars["String"]["input"];
  label: Scalars["String"]["input"];
  type: CustomTagType;
  value: Scalars["String"]["input"];
};

export enum CustomTagType {
  Boolean = "BOOLEAN",
  Date = "DATE",
  Numeric = "NUMERIC",
  Select = "SELECT",
  String = "STRING",
}

export type Customer = {
  assignments: Array<Maybe<CustomerAssignment>>;
  company: Company;
  customTags: Array<Maybe<CustomTag>>;
  documents: Array<Maybe<Document>>;
  id: Scalars["ID"]["output"];
  notes?: Maybe<Array<Maybe<Notes>>>;
  plainTags?: Maybe<Array<Scalars["String"]["output"]>>;
  stage: CustomerStage;
  status: CustomerStatus;
  type: CustomerType;
};

export type CustomerAssignment = {
  __typename?: "CustomerAssignment";
  accountManager?: Maybe<CompanyUser>;
  supportRepresentative?: Maybe<CompanyUser>;
};

export type CustomerBusiness = Customer & {
  __typename?: "CustomerBusiness";
  assignments: Array<Maybe<CustomerAssignment>>;
  basicDetails: BusinessCustomerBasicDetails;
  company: Company;
  customTags: Array<Maybe<CustomTag>>;
  documents: Array<Maybe<Document>>;
  id: Scalars["ID"]["output"];
  notes?: Maybe<Array<Maybe<Notes>>>;
  plainTags?: Maybe<Array<Scalars["String"]["output"]>>;
  stage: CustomerStage;
  status: CustomerStatus;
  type: CustomerType;
};

export type CustomerIndividual = Customer & {
  __typename?: "CustomerIndividual";
  assignments: Array<Maybe<CustomerAssignment>>;
  basicDetails: IndividualCustomerBasicDetails;
  company: Company;
  customTags: Array<Maybe<CustomTag>>;
  documents: Array<Maybe<Document>>;
  id: Scalars["ID"]["output"];
  notes?: Maybe<Array<Maybe<Notes>>>;
  plainTags?: Maybe<Array<Scalars["String"]["output"]>>;
  stage: CustomerStage;
  status: CustomerStatus;
  type: CustomerType;
};

export type CustomerProduct = Product & {
  __typename?: "CustomerProduct";
  /** User provided, can be null */
  company: Company;
  customTags?: Maybe<Array<CustomTag>>;
  customer: Customer;
  description?: Maybe<Scalars["String"]["output"]>;
  dimensions?: Maybe<Array<Dimension>>;
  documents?: Maybe<Array<Document>>;
  id: Scalars["ID"]["output"];
  name: Scalars["String"]["output"];
  pricing: Pricing;
  productCode?: Maybe<Scalars["String"]["output"]>;
  productType: ProductType;
  quote?: Maybe<Quote>;
  status: ProductStatus;
  /** or quoteProduct? */
  tax?: Maybe<Array<Tax>>;
  version: Scalars["ID"]["output"];
};

export type CustomerProductToggleActivationInput = {
  id: Scalars["ID"]["input"];
  version?: InputMaybe<Scalars["ID"]["input"]>;
};

export type CustomerProductUpdateInput = {
  /**  Associated documents */
  customTags?: InputMaybe<Array<CustomTagInput>>;
  /**  User provided unique identifier, can be null */
  customerId: Scalars["ID"]["input"];
  /**  Required as unique identifier along with dimensions */
  description?: InputMaybe<Scalars["String"]["input"]>;
  dimensions?: InputMaybe<Array<DimensionInput>>;
  /**  Pricing information */
  discount: DiscountInput;
  /**  Product dimensions/variants */
  documents?: InputMaybe<Array<DocumentInput>>;
  id: Scalars["ID"]["input"];
  /** To the code it's tagged to. */
  name: Scalars["String"]["input"];
  /**  Custom tags for categorization */
  pricing: PricingInput;
  productCode?: InputMaybe<Scalars["String"]["input"]>;
  /**  Required to identify the customer */
  quoteId: Scalars["ID"]["input"];
};

export enum CustomerStage {
  Churned = "CHURNED",
  ConversionFailed = "CONVERSION_FAILED",
  ConversionInProgress = "CONVERSION_IN_PROGRESS",
  Converted = "CONVERTED",
  Lead = "LEAD",
  Prospect = "PROSPECT",
}

export enum CustomerStatus {
  Active = "ACTIVE",
  Suspended = "SUSPENDED",
}

export enum CustomerType {
  Business = "BUSINESS",
  Individual = "INDIVIDUAL",
}

export type CustomerUpsertAdditionalDetailsInput = {
  assignments?: InputMaybe<Array<AssignmentInput>>;
  customTags?: InputMaybe<Array<CustomTagInput>>;
  customerId: Scalars["ID"]["input"];
  documents?: InputMaybe<Array<DocumentInput>>;
  notes?: InputMaybe<Array<NotesInput>>;
};

export type CustomerUpsertBasicDetailsInput = {
  businessCustomerDetails?: InputMaybe<BusinessCustomerBasicDetailsInput>;
  customerId?: InputMaybe<Scalars["ID"]["input"]>;
  customerStage: CustomerStage;
  customerType: CustomerType;
  individualCustomerDetails?: InputMaybe<IndividualCustomerBasicDetailsInput>;
};

export type Dimension = {
  __typename?: "Dimension";
  key: Scalars["String"]["output"];
  value: Scalars["String"]["output"];
};

export type DimensionInput = {
  key: Scalars["String"]["input"];
  value: Scalars["String"]["input"];
};

export type Discount = {
  __typename?: "Discount";
  /**
   *  Auto generated
   * name: String!
   */
  description?: Maybe<Scalars["String"]["output"]>;
  discountLevel: DiscountLevel;
  discountType: DiscountType;
  discountValue: DiscountValue;
  id: Scalars["ID"]["output"];
};

export type DiscountInput = {
  discountLevel: DiscountLevel;
  discountType: DiscountType;
  discountValue: DiscountValueInput;
};

export enum DiscountLevel {
  Product = "PRODUCT",
  Quote = "QUOTE",
}

export enum DiscountType {
  Amount = "AMOUNT",
  Percentage = "PERCENTAGE",
}

export type DiscountValue = {
  __typename?: "DiscountValue";
  percentage: Scalars["Float"]["output"];
  value: Amount;
};

export type DiscountValueInput = {
  amount: AmountInput;
  percentage: Scalars["Float"]["input"];
};

export type Document = {
  __typename?: "Document";
  description?: Maybe<Scalars["String"]["output"]>;
  file?: Maybe<FileLink>;
  id?: Maybe<Scalars["ID"]["output"]>;
  label?: Maybe<Scalars["String"]["output"]>;
  tags: Array<Scalars["String"]["output"]>;
};

export type DocumentInput = {
  file?: InputMaybe<FileLinkInput>;
  id?: InputMaybe<Scalars["ID"]["input"]>;
};

export type Email = {
  attachments?: Maybe<Array<Scalars["String"]["output"]>>;
  bcc?: Maybe<Array<Scalars["String"]["output"]>>;
  /**  optional as the template can support this as well */
  body?: Maybe<Scalars["String"]["output"]>;
  cc?: Maybe<Array<Scalars["String"]["output"]>>;
  /**  optional as the template can support this as well */
  recipients: Array<Scalars["String"]["output"]>;
  subject?: Maybe<Scalars["String"]["output"]>;
  template: EmailTemplate;
  type: EmailType;
};

export type EmailTemplate = {
  body: Scalars["String"]["output"];
  fromName?: Maybe<Scalars["String"]["output"]>;
  subject: Scalars["String"]["output"];
};

export enum EmailType {
  CompanyEmail = "COMPANY_EMAIL",
  OplatzEmail = "OPLATZ_EMAIL",
}

export enum EntitySize {
  GreaterThanThousand = "GREATER_THAN_THOUSAND",
  HundredToThousand = "HUNDRED_TO_THOUSAND",
  LessThanTen = "LESS_THAN_TEN",
  TenToHundred = "TEN_TO_HUNDRED",
  Unknown = "UNKNOWN",
}

export enum ErrorDetail {
  /**
   * The deadline expired before the operation could complete.
   *
   * For operations that change the state of the system, this error
   * may be returned even if the operation has completed successfully.
   * For example, a successful response from a server could have been
   * delayed long enough for the deadline to expire.
   *
   * HTTP Mapping: 504 Gateway Timeout
   * Error Type: UNAVAILABLE
   */
  DeadlineExceeded = "DEADLINE_EXCEEDED",
  /**
   * The server detected that the client is exhibiting a behavior that
   * might be generating excessive load.
   *
   * HTTP Mapping: 420 Enhance Your Calm
   * Error Type: UNAVAILABLE
   */
  EnhanceYourCalm = "ENHANCE_YOUR_CALM",
  /**
   * The requested field is not found in the schema.
   *
   * This differs from `NOT_FOUND` in that `NOT_FOUND` should be used when a
   * query is valid, but is unable to return a result (if, for example, a
   * specific video id doesn't exist). `FIELD_NOT_FOUND` is intended to be
   * returned by the server to signify that the requested field is not known to exist.
   * This may be returned in lieu of failing the entire query.
   * See also `PERMISSION_DENIED` for cases where the
   * requested field is invalid only for the given user or class of users.
   *
   * HTTP Mapping: 404 Not Found
   * Error Type: BAD_REQUEST
   */
  FieldNotFound = "FIELD_NOT_FOUND",
  /**
   * The client specified an invalid argument.
   *
   * Note that this differs from `FAILED_PRECONDITION`.
   * `INVALID_ARGUMENT` indicates arguments that are problematic
   * regardless of the state of the system (e.g., a malformed file name).
   *
   * HTTP Mapping: 400 Bad Request
   * Error Type: BAD_REQUEST
   */
  InvalidArgument = "INVALID_ARGUMENT",
  /**
   * The provided cursor is not valid.
   *
   * The most common usage for this error is when a client is paginating
   * through a list that uses stateful cursors. In that case, the provided
   * cursor may be expired.
   *
   * HTTP Mapping: 404 Not Found
   * Error Type: NOT_FOUND
   */
  InvalidCursor = "INVALID_CURSOR",
  /**
   * Unable to perform operation because a required resource is missing.
   *
   * Example: Client is attempting to refresh a list, but the specified
   * list is expired. This requires an action by the client to get a new list.
   *
   * If the user is simply trying GET a resource that is not found,
   * use the NOT_FOUND error type. FAILED_PRECONDITION.MISSING_RESOURCE
   * is to be used particularly when the user is performing an operation
   * that requires a particular resource to exist.
   *
   * HTTP Mapping: 400 Bad Request or 500 Internal Server Error
   * Error Type: FAILED_PRECONDITION
   */
  MissingResource = "MISSING_RESOURCE",
  /**
   * Service Error.
   *
   * There is a problem with an upstream service.
   *
   * This may be returned if a gateway receives an unknown error from a service
   * or if a service is unreachable.
   * If a request times out which waiting on a response from a service,
   * `DEADLINE_EXCEEDED` may be returned instead.
   * If a service returns a more specific error Type, the specific error Type may
   * be returned instead.
   *
   * HTTP Mapping: 502 Bad Gateway
   * Error Type: UNAVAILABLE
   */
  ServiceError = "SERVICE_ERROR",
  /**
   * Request failed due to network errors.
   *
   * HTTP Mapping: 503 Unavailable
   * Error Type: UNAVAILABLE
   */
  TcpFailure = "TCP_FAILURE",
  /**
   * Request throttled based on server concurrency limits.
   *
   * HTTP Mapping: 503 Unavailable
   * Error Type: UNAVAILABLE
   */
  ThrottledConcurrency = "THROTTLED_CONCURRENCY",
  /**
   * Request throttled based on server CPU limits
   *
   * HTTP Mapping: 503 Unavailable.
   * Error Type: UNAVAILABLE
   */
  ThrottledCpu = "THROTTLED_CPU",
  /**
   * The server detected that the client is exhibiting a behavior that
   * might be generating excessive load.
   *
   * HTTP Mapping: 429 Too Many Requests
   * Error Type: UNAVAILABLE
   */
  TooManyRequests = "TOO_MANY_REQUESTS",
  /**
   * The operation is not implemented or is not currently supported/enabled.
   *
   * HTTP Mapping: 501 Not Implemented
   * Error Type: BAD_REQUEST
   */
  Unimplemented = "UNIMPLEMENTED",
  /**
   * Unknown error.
   *
   * This error should only be returned when no other error detail applies.
   * If a client sees an unknown errorDetail, it will be interpreted as UNKNOWN.
   *
   * HTTP Mapping: 500 Internal Server Error
   */
  Unknown = "UNKNOWN",
}

export enum ErrorType {
  /**
   * Bad Request.
   *
   * There is a problem with the request.
   * Retrying the same request is not likely to succeed.
   * An example would be a query or argument that cannot be deserialized.
   *
   * HTTP Mapping: 400 Bad Request
   */
  BadRequest = "BAD_REQUEST",
  /**
   * The operation was rejected because the system is not in a state
   * required for the operation's execution.  For example, the directory
   * to be deleted is non-empty, an rmdir operation is applied to
   * a non-directory, etc.
   *
   * Service implementers can use the following guidelines to decide
   * between `FAILED_PRECONDITION` and `UNAVAILABLE`:
   *
   * - Use `UNAVAILABLE` if the client can retry just the failing call.
   * - Use `FAILED_PRECONDITION` if the client should not retry until
   * the system state has been explicitly fixed.  E.g., if an "rmdir"
   *      fails because the directory is non-empty, `FAILED_PRECONDITION`
   * should be returned since the client should not retry unless
   * the files are deleted from the directory.
   *
   * HTTP Mapping: 400 Bad Request or 500 Internal Server Error
   */
  FailedPrecondition = "FAILED_PRECONDITION",
  /**
   * Internal error.
   *
   * An unexpected internal error was encountered. This means that some
   * invariants expected by the underlying system have been broken.
   * This error code is reserved for serious errors.
   *
   * HTTP Mapping: 500 Internal Server Error
   */
  Internal = "INTERNAL",
  /**
   * The requested entity was not found.
   *
   * This could apply to a resource that has never existed (e.g. bad resource id),
   * or a resource that no longer exists (e.g. cache expired.)
   *
   * Note to server developers: if a request is denied for an entire class
   * of users, such as gradual feature rollout or undocumented allowlist,
   * `NOT_FOUND` may be used. If a request is denied for some users within
   * a class of users, such as user-based access control, `PERMISSION_DENIED`
   * must be used.
   *
   * HTTP Mapping: 404 Not Found
   */
  NotFound = "NOT_FOUND",
  /**
   * The caller does not have permission to execute the specified
   * operation.
   *
   * `PERMISSION_DENIED` must not be used for rejections
   * caused by exhausting some resource or quota.
   * `PERMISSION_DENIED` must not be used if the caller
   * cannot be identified (use `UNAUTHENTICATED`
   * instead for those errors).
   *
   * This error Type does not imply the
   * request is valid or the requested entity exists or satisfies
   * other pre-conditions.
   *
   * HTTP Mapping: 403 Forbidden
   */
  PermissionDenied = "PERMISSION_DENIED",
  /**
   * The request does not have valid authentication credentials.
   *
   * This is intended to be returned only for routes that require
   * authentication.
   *
   * HTTP Mapping: 401 Unauthorized
   */
  Unauthenticated = "UNAUTHENTICATED",
  /**
   * Currently Unavailable.
   *
   * The service is currently unavailable.  This is most likely a
   * transient condition, which can be corrected by retrying with
   * a backoff.
   *
   * HTTP Mapping: 503 Unavailable
   */
  Unavailable = "UNAVAILABLE",
  /**
   * Unknown error.
   *
   * For example, this error may be returned when
   * an error code received from another address space belongs to
   * an error space that is not known in this address space.  Also
   * errors raised by APIs that do not return enough error information
   * may be converted to this error.
   *
   * If a client sees an unknown errorType, it will be interpreted as UNKNOWN.
   * Unknown errors MUST NOT trigger any special behavior. These MAY be treated
   * by an implementation as being equivalent to INTERNAL.
   *
   * When possible, a more specific error should be provided.
   *
   * HTTP Mapping: 520 Unknown Error
   */
  Unknown = "UNKNOWN",
}

export type FileLink = {
  __typename?: "FileLink";
  signedReadURL?: Maybe<Scalars["String"]["output"]>;
  signedWriteURL?: Maybe<Scalars["String"]["output"]>;
};

export type FileLinkInput = {
  signedReadURL?: InputMaybe<Scalars["String"]["input"]>;
  signedWriteURL?: InputMaybe<Scalars["String"]["input"]>;
};

export type GetCustomersQueryFilterInput = {
  customerType?: InputMaybe<CustomerType>;
  stage?: InputMaybe<CustomerStage>;
  status?: InputMaybe<CustomerStatus>;
};

export type GetMasterProductFilters = {
  status?: InputMaybe<ProductStatus>;
};

export type GetQuotesFilters = {
  status?: InputMaybe<QuoteStatus>;
};

export type IndividualCustomerBasicDetails = {
  __typename?: "IndividualCustomerBasicDetails";
  address?: Maybe<Scalars["String"]["output"]>;
  contactDetails: ContactDetails;
  referralSource?: Maybe<Scalars["String"]["output"]>;
};

export type IndividualCustomerBasicDetailsInput = {
  address?: InputMaybe<Scalars["String"]["input"]>;
  emailAddress: Scalars["String"]["input"];
  fullName: Scalars["String"]["input"];
  phoneNumber?: InputMaybe<Scalars["String"]["input"]>;
  referralSource?: InputMaybe<Scalars["String"]["input"]>;
};

export type Margin = {
  __typename?: "Margin";
  absoluteAmount: Amount;
  percentage: Scalars["Float"]["output"];
};

export type MarginInput = {
  absoluteAmount: AmountInput;
  percentage: Scalars["Float"]["input"];
};

export type MasterProduct = Product & {
  __typename?: "MasterProduct";
  /** User provided, can be null */
  company: Company;
  customTags?: Maybe<Array<CustomTag>>;
  description?: Maybe<Scalars["String"]["output"]>;
  dimensions?: Maybe<Array<Dimension>>;
  documents?: Maybe<Array<Document>>;
  id: Scalars["ID"]["output"];
  name: Scalars["String"]["output"];
  pricing: Pricing;
  productCode?: Maybe<Scalars["String"]["output"]>;
  productType: ProductType;
  status: ProductStatus;
  tax?: Maybe<Array<Tax>>;
  version: Scalars["ID"]["output"];
};

export type MasterProductToggleActivationInput = {
  id: Scalars["ID"]["input"];
  version?: InputMaybe<Scalars["ID"]["input"]>;
};

export type MasterProductUpsertAdditionalDetailsInput = {
  customTags?: InputMaybe<Array<CustomTagInput>>;
  documents?: InputMaybe<Array<DocumentInput>>;
  masterProductId: Scalars["ID"]["input"];
};

export type MasterProductUpsertInput = {
  description?: InputMaybe<Scalars["String"]["input"]>;
  dimensions?: InputMaybe<Array<DimensionInput>>;
  id?: InputMaybe<Scalars["ID"]["input"]>;
  name: Scalars["String"]["input"];
  pricing: PricingInput;
  productCode: Scalars["String"]["input"];
};

export type Mutation = {
  __typename?: "Mutation";
  companyCreateBasicDetails?: Maybe<Company>;
  companyUpdateBasicDetails?: Maybe<Company>;
  companyUserActivate: CompanyUser;
  companyUserCreate: CompanyUser;
  companyUserDeactivate: TaskResponse;
  companyUserInvite: CompanyUser;
  customerProductToggleActivation: CustomerProduct;
  customerProductUpdate: CustomerProduct;
  customerUpsertAdditionalDetails?: Maybe<Customer>;
  customerUpsertBasicDetails?: Maybe<Customer>;
  masterProductToggleActivation: MasterProduct;
  masterProductUpsertAdditionalDetails: MasterProduct;
  masterProductUpsertDetails: MasterProduct;
  ok?: Maybe<Scalars["Boolean"]["output"]>;
  quoteUpsert: Quote;
};

export type MutationCompanyCreateBasicDetailsArgs = {
  input: CompanyCreateBasicDetailsInput;
};

export type MutationCompanyUpdateBasicDetailsArgs = {
  input: CompanyUpdateBasicDetailsInput;
};

export type MutationCompanyUserCreateArgs = {
  input: CompanyUserCreateInput;
};

export type MutationCompanyUserDeactivateArgs = {
  companyUserId: Scalars["ID"]["input"];
};

export type MutationCompanyUserInviteArgs = {
  companyUserId: Scalars["ID"]["input"];
};

export type MutationCustomerProductToggleActivationArgs = {
  input: CustomerProductToggleActivationInput;
};

export type MutationCustomerProductUpdateArgs = {
  input: CustomerProductUpdateInput;
};

export type MutationCustomerUpsertAdditionalDetailsArgs = {
  input: CustomerUpsertAdditionalDetailsInput;
};

export type MutationCustomerUpsertBasicDetailsArgs = {
  input: CustomerUpsertBasicDetailsInput;
};

export type MutationMasterProductToggleActivationArgs = {
  input: MasterProductToggleActivationInput;
};

export type MutationMasterProductUpsertAdditionalDetailsArgs = {
  input: MasterProductUpsertAdditionalDetailsInput;
};

export type MutationMasterProductUpsertDetailsArgs = {
  input: MasterProductUpsertInput;
};

export type MutationQuoteUpsertArgs = {
  input: QuoteUpsertInput;
};

export type Notes = {
  __typename?: "Notes";
  /**  to support client dto transformations */
  content: Scalars["String"]["output"];
  id?: Maybe<Scalars["ID"]["output"]>;
  tags: Array<Scalars["String"]["output"]>;
};

export type NotesInput = {
  content: Scalars["String"]["input"];
  id?: InputMaybe<Scalars["ID"]["input"]>;
  tags: Array<Scalars["String"]["input"]>;
};

export type OplatzEmail = Email & {
  __typename?: "OplatzEmail";
  attachments?: Maybe<Array<Scalars["String"]["output"]>>;
  bcc?: Maybe<Array<Scalars["String"]["output"]>>;
  /**  optional as the template can support this as well */
  body?: Maybe<Scalars["String"]["output"]>;
  cc?: Maybe<Array<Scalars["String"]["output"]>>;
  /**  optional as the template can support this as well */
  recipients: Array<Scalars["String"]["output"]>;
  subject?: Maybe<Scalars["String"]["output"]>;
  template: OplatzEmailTemplate;
  type: EmailType;
};

export type OplatzEmailTemplate = EmailTemplate & {
  __typename?: "OplatzEmailTemplate";
  body: Scalars["String"]["output"];
  fromName?: Maybe<Scalars["String"]["output"]>;
  subject: Scalars["String"]["output"];
  type: OplatzEmailTemplateType;
};

export enum OplatzEmailTemplateType {
  OplatzCompanyCreated = "OPLATZ_COMPANY_CREATED",
}

export type Pricing = {
  __typename?: "Pricing";
  chargePolicy: ChargePolicy;
  costPrice: Amount;
  discount: Array<Maybe<Discount>>;
  id: Scalars["ID"]["output"];
  /**  price at which we buy from vendor or cost of rendering the service */
  listPrice: Amount;
  /**  price @ which we sell to customer after discount, will be same as list price by default */
  margin: Margin;
  /**  default price to sell at to customer */
  sellingPrice: Amount;
  /**
   *  margin = (sellingPrice - costPrice) / sellingPrice
   * billingFrequency
   */
  unit: ProductUnit;
};

export type PricingInput = {
  chargePolicy: ChargePolicy;
  costPrice: AmountInput;
  /**  unit of measurement for the product, e.g: 1 */
  discounts?: InputMaybe<Array<DiscountInput>>;
  id?: InputMaybe<Scalars["ID"]["input"]>;
  /**  price at which we buy from vendor or cost of rendering the service */
  listPrice: AmountInput;
  /**  price @ which we sell to customer after discount, will be same as list price by default */
  productUnit: ProductUnitInput;
  /**  default price to sell at to customer */
  sellingPrice: AmountInput;
  /**  discounts applicable on the product, e.g: 10% off */
  tax?: InputMaybe<Array<TaxInput>>;
};

export type Product = {
  company: Company;
  customTags?: Maybe<Array<CustomTag>>;
  /**  Will be used as unique identifier along with dimensions, if productCode is null */
  description?: Maybe<Scalars["String"]["output"]>;
  dimensions?: Maybe<Array<Dimension>>;
  documents?: Maybe<Array<Document>>;
  id: Scalars["ID"]["output"];
  /**  User provided, unique identifier along with dimensions, it can be null */
  name: Scalars["String"]["output"];
  pricing: Pricing;
  productCode?: Maybe<Scalars["String"]["output"]>;
  productType: ProductType;
  status: ProductStatus;
  tax?: Maybe<Array<Tax>>;
  version: Scalars["ID"]["output"];
};

export enum ProductStatus {
  Active = "ACTIVE",
  Created = "CREATED",
  Inactive = "INACTIVE",
}

export enum ProductType {
  Customer = "CUSTOMER",
  Master = "MASTER",
  Quote = "QUOTE",
}

export type ProductUnit = {
  __typename?: "ProductUnit";
  unit: Scalars["Float"]["output"];
  /**  unit of measurement for the product, e.g: 1 */
  unitType: Scalars["String"]["output"];
};

export type ProductUnitInput = {
  unit: Scalars["Float"]["input"];
  /**  unit of measurement for the product, e.g: 1 */
  unitType: Scalars["String"]["input"];
};

export type Query = {
  __typename?: "Query";
  _service: _Service;
  getCompany?: Maybe<Company>;
  getCompanyUser?: Maybe<CompanyUser>;
  getCustomer?: Maybe<Customer>;
  getCustomers: Array<Customer>;
  masterProductGetById: MasterProduct;
  masterProductsGet: Array<MasterProduct>;
  ok?: Maybe<Scalars["Boolean"]["output"]>;
  quotesGet: Array<Quote>;
};

export type QueryGetCustomerArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryGetCustomersArgs = {
  filters?: InputMaybe<GetCustomersQueryFilterInput>;
};

export type QueryMasterProductGetByIdArgs = {
  id: Scalars["ID"]["input"];
};

export type QueryMasterProductsGetArgs = {
  filters?: InputMaybe<GetMasterProductFilters>;
};

export type QueryQuotesGetArgs = {
  filters?: InputMaybe<GetQuotesFilters>;
};

export type Quote = {
  __typename?: "Quote";
  address: QuoteAddress;
  assignedCompanyUser?: Maybe<CompanyUser>;
  /** calculated value from the quote product items */
  assignments?: Maybe<Array<QuoteAssignment>>;
  company: Company;
  createdDate: Scalars["DateTime"]["output"];
  customTags?: Maybe<Array<CustomTag>>;
  customer: Customer;
  date: QuoteDate;
  description?: Maybe<Scalars["String"]["output"]>;
  /** calculated value from the quote product items */
  discounts: Array<Discount>;
  documents?: Maybe<Array<Document>>;
  id: Scalars["ID"]["output"];
  /** the person example, sales etc to which this quote is assigned. */
  products: Array<QuoteProduct>;
  quoteTotalDiscountAmount: Amount;
  quoteTotalListPrice: Amount;
  /**  quote products.listPrice */
  quoteTotalSellingPrice: Amount;
  quoteTotalTaxAmount: Amount;
  status: QuoteStatus;
  version: Scalars["ID"]["output"];
};

export type QuoteAddress = {
  __typename?: "QuoteAddress";
  fromAddress: Scalars["String"]["output"];
  toAddress: Scalars["String"]["output"];
};

export type QuoteAddressInput = {
  fromAddress: Scalars["String"]["input"];
  toAddress: Scalars["String"]["input"];
};

export type QuoteAssignment = {
  __typename?: "QuoteAssignment";
  customerSuccessManger?: Maybe<CompanyUser>;
  salesExecutive?: Maybe<CompanyUser>;
};

export type QuoteAssignmentInput = {
  customerSuccessManger?: InputMaybe<Scalars["ID"]["input"]>;
  salesExecutive?: InputMaybe<Scalars["ID"]["input"]>;
};

export type QuoteDate = {
  __typename?: "QuoteDate";
  validFrom: Scalars["DateTime"]["output"];
  validTill?: Maybe<Scalars["DateTime"]["output"]>;
};

export type QuoteDateInput = {
  validFrom: Scalars["DateTime"]["input"];
  validTill?: InputMaybe<Scalars["DateTime"]["input"]>;
};

export type QuoteProduct = Product & {
  __typename?: "QuoteProduct";
  company: Company;
  customTags?: Maybe<Array<CustomTag>>;
  description?: Maybe<Scalars["String"]["output"]>;
  dimensions?: Maybe<Array<Dimension>>;
  documents?: Maybe<Array<Document>>;
  id: Scalars["ID"]["output"];
  /** User provided, can be null */
  name: Scalars["String"]["output"];
  pricing: Pricing;
  productCode?: Maybe<Scalars["String"]["output"]>;
  productType: ProductType;
  quote: Quote;
  referenceProduct?: Maybe<QuoteProductReferenceProduct>;
  status: ProductStatus;
  tax?: Maybe<Array<Tax>>;
  /** we don't want that, right? Shall we always keep it CREATED? */
  version: Scalars["ID"]["output"];
};

export type QuoteProductCreateInput = {
  customTags?: InputMaybe<Array<CustomTagInput>>;
  customerId: Scalars["ID"]["input"];
  description?: InputMaybe<Scalars["String"]["input"]>;
  dimensions?: InputMaybe<Array<DimensionInput>>;
  documents?: InputMaybe<Array<DocumentInput>>;
  /** non mandatory. */
  name: Scalars["String"]["input"];
  pricing: PricingInput;
  productCode: Scalars["String"]["input"];
  referenceProduct?: InputMaybe<QuoteProductReferenceProductInput>;
  tax?: InputMaybe<Array<TaxInput>>;
};

export type QuoteProductReferenceProduct = {
  __typename?: "QuoteProductReferenceProduct";
  id?: Maybe<Scalars["ID"]["output"]>;
  type?: Maybe<ProductType>;
};

export type QuoteProductReferenceProductInput = {
  id: Scalars["ID"]["input"];
  type: ProductType;
};

export enum QuoteStatus {
  Accepted = "ACCEPTED",
  Activated = "ACTIVATED",
  Created = "CREATED",
  Deleted = "DELETED",
  Rejected = "REJECTED",
  Sent = "SENT",
}

export type QuoteUpsertInput = {
  addressInput: QuoteAddressInput;
  assignments?: InputMaybe<Array<QuoteAssignmentInput>>;
  customTags?: InputMaybe<Array<CustomTagInput>>;
  customerId: Scalars["ID"]["input"];
  dateInput: QuoteDateInput;
  description?: InputMaybe<Scalars["String"]["input"]>;
  discounts?: InputMaybe<Array<DiscountInput>>;
  documents?: InputMaybe<Array<DocumentInput>>;
  id?: InputMaybe<Scalars["ID"]["input"]>;
  paymentTerms?: InputMaybe<Scalars["String"]["input"]>;
  quoteProductsInput: Array<QuoteProductCreateInput>;
};

export type TaskResponse = {
  __typename?: "TaskResponse";
  message: Scalars["String"]["output"];
  success: Scalars["Boolean"]["output"];
};

export type Tax = {
  __typename?: "Tax";
  amount: Amount;
  name?: Maybe<Scalars["String"]["output"]>;
  percentage: Scalars["Float"]["output"];
};

export type TaxInput = {
  amount: AmountInput;
  name?: InputMaybe<Scalars["String"]["input"]>;
  percentage: Scalars["Float"]["input"];
};

export type _Service = {
  __typename?: "_Service";
  sdl: Scalars["String"]["output"];
};

export type CompanyUserCreateMutationVariables = Exact<{
  input: CompanyUserCreateInput;
}>;

export type CompanyUserCreateMutation = {
  __typename?: "Mutation";
  companyUserCreate: {
    __typename?: "CompanyUser";
    id: string;
    name: string;
    email: string;
    status: CompanyUserStatus;
    roles: Array<CompanyUserRole | null>;
  };
};

export type CompanyUserInviteMutationVariables = Exact<{
  companyUserId: Scalars["ID"]["input"];
}>;

export type CompanyUserInviteMutation = {
  __typename?: "Mutation";
  companyUserInvite: {
    __typename?: "CompanyUser";
    id: string;
    name: string;
    email: string;
    status: CompanyUserStatus;
    roles: Array<CompanyUserRole | null>;
  };
};

export type CompanyUserDeactivateMutationVariables = Exact<{
  companyUserId: Scalars["ID"]["input"];
}>;

export type CompanyUserDeactivateMutation = {
  __typename?: "Mutation";
  companyUserDeactivate: {
    __typename?: "TaskResponse";
    success: boolean;
    message: string;
  };
};

export type CompanyUserActivateMutationVariables = Exact<{
  [key: string]: never;
}>;

export type CompanyUserActivateMutation = {
  __typename?: "Mutation";
  companyUserActivate: {
    __typename?: "CompanyUser";
    id: string;
    name: string;
    email: string;
    status: CompanyUserStatus;
    roles: Array<CompanyUserRole | null>;
  };
};

export type CreateCompanyBasicDetailsMutationVariables = Exact<{
  input: CompanyCreateBasicDetailsInput;
}>;

export type CreateCompanyBasicDetailsMutation = {
  __typename?: "Mutation";
  companyCreateBasicDetails?: {
    __typename?: "Company";
    id: string;
    basicDetails?: {
      __typename?: "CompanyBasicDetails";
      name: string;
      address?: string | null;
      phoneNumber?: string | null;
      email: string;
      website: string;
      industry?: string | null;
      size?: EntitySize | null;
    } | null;
  } | null;
};

export type UpdateCompanyBasicDetailsMutationVariables = Exact<{
  input: CompanyUpdateBasicDetailsInput;
}>;

export type UpdateCompanyBasicDetailsMutation = {
  __typename?: "Mutation";
  companyUpdateBasicDetails?: {
    __typename?: "Company";
    id: string;
    basicDetails?: {
      __typename?: "CompanyBasicDetails";
      name: string;
      address?: string | null;
      phoneNumber?: string | null;
      email: string;
      website: string;
      industry?: string | null;
      size?: EntitySize | null;
    } | null;
  } | null;
};

export type CustomerUpsertBasicDetailsMutationVariables = Exact<{
  input: CustomerUpsertBasicDetailsInput;
}>;

export type CustomerUpsertBasicDetailsMutation = {
  __typename?: "Mutation";
  customerUpsertBasicDetails?:
    | { __typename?: "CustomerBusiness"; id: string }
    | { __typename?: "CustomerIndividual"; id: string }
    | null;
};

export type CustomerUpsertAdditionalDetailsMutationVariables = Exact<{
  input: CustomerUpsertAdditionalDetailsInput;
}>;

export type CustomerUpsertAdditionalDetailsMutation = {
  __typename?: "Mutation";
  customerUpsertAdditionalDetails?:
    | { __typename?: "CustomerBusiness"; id: string }
    | { __typename?: "CustomerIndividual"; id: string }
    | null;
};

export type MasterProductUpsertDetailsMutationVariables = Exact<{
  input: MasterProductUpsertInput;
}>;

export type MasterProductUpsertDetailsMutation = {
  __typename?: "Mutation";
  masterProductUpsertDetails: {
    __typename?: "MasterProduct";
    id: string;
    pricing: { __typename?: "Pricing"; id: string };
  };
};

export type MasterProductUpsertAdditionalDetailsMutationVariables = Exact<{
  input: MasterProductUpsertAdditionalDetailsInput;
}>;

export type MasterProductUpsertAdditionalDetailsMutation = {
  __typename?: "Mutation";
  masterProductUpsertAdditionalDetails: {
    __typename?: "MasterProduct";
    id: string;
    pricing: { __typename?: "Pricing"; id: string };
  };
};

export type ProposalProductUpsertMutationVariables = Exact<{
  input: QuoteUpsertInput;
}>;

export type ProposalProductUpsertMutation = {
  __typename?: "Mutation";
  quoteUpsert: { __typename?: "Quote"; id: string; status: QuoteStatus };
};

export type OkQueryVariables = Exact<{ [key: string]: never }>;

export type OkQuery = { __typename?: "Query"; ok?: boolean | null };

export type GetCompanyQueryVariables = Exact<{ [key: string]: never }>;

export type GetCompanyQuery = {
  __typename?: "Query";
  getCompany?: {
    __typename?: "Company";
    id: string;
    name?: string | null;
    status?: CompanyStatus | null;
    onboarding?: {
      __typename?: "CompanyOnboarding";
      id: string;
      pendingSteps: Array<{
        __typename?: "CompanyOnboardingStep";
        stepType: CompanyOnboardingStepType;
        mandatory: boolean;
        completed: boolean;
      } | null>;
      completedSteps: Array<{
        __typename?: "CompanyOnboardingStep";
        stepType: CompanyOnboardingStepType;
        mandatory: boolean;
        completed: boolean;
      } | null>;
    } | null;
  } | null;
};

export type GetCompanyBasicDetailsQueryVariables = Exact<{
  [key: string]: never;
}>;

export type GetCompanyBasicDetailsQuery = {
  __typename?: "Query";
  getCompany?: {
    __typename?: "Company";
    id: string;
    basicDetails?: {
      __typename?: "CompanyBasicDetails";
      name: string;
      address?: string | null;
      email: string;
      website: string;
      phoneNumber?: string | null;
      industry?: string | null;
      size?: EntitySize | null;
    } | null;
  } | null;
};

export type GetCompanyUsersQueryVariables = Exact<{ [key: string]: never }>;

export type GetCompanyUsersQuery = {
  __typename?: "Query";
  getCompany?: {
    __typename?: "Company";
    id: string;
    users?: Array<{
      __typename?: "CompanyUser";
      id: string;
      name: string;
      email: string;
      status: CompanyUserStatus;
      roles: Array<CompanyUserRole | null>;
    } | null> | null;
  } | null;
};

export type GetCompanyUserQueryVariables = Exact<{ [key: string]: never }>;

export type GetCompanyUserQuery = {
  __typename?: "Query";
  getCompanyUser?: {
    __typename?: "CompanyUser";
    id: string;
    name: string;
    email: string;
    status: CompanyUserStatus;
    roles: Array<CompanyUserRole | null>;
  } | null;
};

export type GetAllCustomersQueryVariables = Exact<{
  status?: InputMaybe<CustomerStatus>;
  stage?: InputMaybe<CustomerStage>;
  customerType?: InputMaybe<CustomerType>;
}>;

export type GetAllCustomersQuery = {
  __typename?: "Query";
  getCustomers: Array<
    | {
        __typename?: "CustomerBusiness";
        id: string;
        status: CustomerStatus;
        stage: CustomerStage;
        type: CustomerType;
        basicDetails: {
          __typename?: "BusinessCustomerBasicDetails";
          legalName: string;
          website: string;
          size: EntitySize;
          industry?: string | null;
          referralSource?: string | null;
          contactDetails: {
            __typename?: "ContactDetails";
            contactType: ContactType;
            name: string;
            title?: string | null;
            email: string;
            phoneNo?: string | null;
          };
        };
        company: { __typename?: "Company"; id: string };
      }
    | {
        __typename?: "CustomerIndividual";
        id: string;
        status: CustomerStatus;
        stage: CustomerStage;
        type: CustomerType;
        basicDetails: {
          __typename?: "IndividualCustomerBasicDetails";
          referralSource?: string | null;
          contactDetails: {
            __typename?: "ContactDetails";
            contactType: ContactType;
            name: string;
            title?: string | null;
            email: string;
            phoneNo?: string | null;
          };
        };
        company: { __typename?: "Company"; id: string };
      }
  >;
};

export type GetCustomerByIdQueryVariables = Exact<{
  id: Scalars["ID"]["input"];
}>;

export type GetCustomerByIdQuery = {
  __typename?: "Query";
  getCustomer?:
    | {
        __typename?: "CustomerBusiness";
        id: string;
        status: CustomerStatus;
        stage: CustomerStage;
        type: CustomerType;
        basicDetails: {
          __typename?: "BusinessCustomerBasicDetails";
          legalName: string;
          website: string;
          size: EntitySize;
          industry?: string | null;
          referralSource?: string | null;
          contactDetails: {
            __typename?: "ContactDetails";
            contactType: ContactType;
            name: string;
            title?: string | null;
            email: string;
            phoneNo?: string | null;
          };
        };
        notes?: Array<{
          __typename?: "Notes";
          id?: string | null;
          content: string;
          tags: Array<string>;
        } | null> | null;
        assignments: Array<{
          __typename?: "CustomerAssignment";
          accountManager?: {
            __typename?: "CompanyUser";
            id: string;
            name: string;
            email: string;
          } | null;
          supportRepresentative?: {
            __typename?: "CompanyUser";
            id: string;
            name: string;
            email: string;
          } | null;
        } | null>;
        customTags: Array<{
          __typename?: "CustomTag";
          id?: string | null;
          key: string;
          label: string;
          value: string;
          type: CustomTagType;
          description?: string | null;
        } | null>;
        company: { __typename?: "Company"; id: string };
      }
    | {
        __typename?: "CustomerIndividual";
        id: string;
        status: CustomerStatus;
        stage: CustomerStage;
        type: CustomerType;
        basicDetails: {
          __typename?: "IndividualCustomerBasicDetails";
          referralSource?: string | null;
          contactDetails: {
            __typename?: "ContactDetails";
            contactType: ContactType;
            name: string;
            title?: string | null;
            email: string;
            phoneNo?: string | null;
          };
        };
        notes?: Array<{
          __typename?: "Notes";
          id?: string | null;
          content: string;
          tags: Array<string>;
        } | null> | null;
        assignments: Array<{
          __typename?: "CustomerAssignment";
          accountManager?: {
            __typename?: "CompanyUser";
            id: string;
            name: string;
            email: string;
          } | null;
          supportRepresentative?: {
            __typename?: "CompanyUser";
            id: string;
            name: string;
            email: string;
          } | null;
        } | null>;
        customTags: Array<{
          __typename?: "CustomTag";
          id?: string | null;
          key: string;
          label: string;
          value: string;
          type: CustomTagType;
          description?: string | null;
        } | null>;
        company: { __typename?: "Company"; id: string };
      }
    | null;
};

export type GetAllProductsQueryVariables = Exact<{
  status?: InputMaybe<ProductStatus>;
}>;

export type GetAllProductsQuery = {
  __typename?: "Query";
  masterProductsGet: Array<{
    __typename?: "MasterProduct";
    id: string;
    name: string;
    description?: string | null;
    status: ProductStatus;
    productCode?: string | null;
    dimensions?: Array<{
      __typename?: "Dimension";
      key: string;
      value: string;
    }> | null;
    pricing: {
      __typename?: "Pricing";
      id: string;
      chargePolicy: ChargePolicy;
      costPrice: { __typename?: "Amount"; value: number; currency: Currency };
      listPrice: { __typename?: "Amount"; value: number; currency: Currency };
      sellingPrice: {
        __typename?: "Amount";
        value: number;
        currency: Currency;
      };
      unit: { __typename?: "ProductUnit"; unit: number; unitType: string };
      discount: Array<{
        __typename?: "Discount";
        id: string;
        discountType: DiscountType;
        discountValue: {
          __typename?: "DiscountValue";
          percentage: number;
          value: { __typename?: "Amount"; value: number; currency: Currency };
        };
      } | null>;
    };
  }>;
};

export type GetProductByIdQueryVariables = Exact<{
  id: Scalars["ID"]["input"];
}>;

export type GetProductByIdQuery = {
  __typename?: "Query";
  masterProductGetById: {
    __typename?: "MasterProduct";
    id: string;
    name: string;
    description?: string | null;
    status: ProductStatus;
    productCode?: string | null;
    dimensions?: Array<{
      __typename?: "Dimension";
      key: string;
      value: string;
    }> | null;
    documents?: Array<{
      __typename?: "Document";
      id?: string | null;
      file?: {
        __typename?: "FileLink";
        signedReadURL?: string | null;
        signedWriteURL?: string | null;
      } | null;
    }> | null;
    customTags?: Array<{
      __typename?: "CustomTag";
      id?: string | null;
      key: string;
      label: string;
      value: string;
      type: CustomTagType;
      description?: string | null;
    }> | null;
    pricing: {
      __typename?: "Pricing";
      id: string;
      chargePolicy: ChargePolicy;
      costPrice: { __typename?: "Amount"; value: number; currency: Currency };
      listPrice: { __typename?: "Amount"; value: number; currency: Currency };
      sellingPrice: {
        __typename?: "Amount";
        value: number;
        currency: Currency;
      };
      unit: { __typename?: "ProductUnit"; unit: number; unitType: string };
      discount: Array<{
        __typename?: "Discount";
        id: string;
        discountType: DiscountType;
        discountValue: {
          __typename?: "DiscountValue";
          percentage: number;
          value: { __typename?: "Amount"; value: number; currency: Currency };
        };
      } | null>;
    };
  };
};

export const CompanyUserCreateDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "CompanyUserCreate" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "input" },
          },
          type: {
            kind: "NonNullType",
            type: {
              kind: "NamedType",
              name: { kind: "Name", value: "CompanyUserCreateInput" },
            },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "companyUserCreate" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "input" },
                value: {
                  kind: "Variable",
                  name: { kind: "Name", value: "input" },
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
                { kind: "Field", name: { kind: "Name", value: "email" } },
                { kind: "Field", name: { kind: "Name", value: "status" } },
                { kind: "Field", name: { kind: "Name", value: "roles" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  CompanyUserCreateMutation,
  CompanyUserCreateMutationVariables
>;
export const CompanyUserInviteDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "CompanyUserInvite" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "companyUserId" },
          },
          type: {
            kind: "NonNullType",
            type: { kind: "NamedType", name: { kind: "Name", value: "ID" } },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "companyUserInvite" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "companyUserId" },
                value: {
                  kind: "Variable",
                  name: { kind: "Name", value: "companyUserId" },
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
                { kind: "Field", name: { kind: "Name", value: "email" } },
                { kind: "Field", name: { kind: "Name", value: "status" } },
                { kind: "Field", name: { kind: "Name", value: "roles" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  CompanyUserInviteMutation,
  CompanyUserInviteMutationVariables
>;
export const CompanyUserDeactivateDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "CompanyUserDeactivate" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "companyUserId" },
          },
          type: {
            kind: "NonNullType",
            type: { kind: "NamedType", name: { kind: "Name", value: "ID" } },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "companyUserDeactivate" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "companyUserId" },
                value: {
                  kind: "Variable",
                  name: { kind: "Name", value: "companyUserId" },
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "success" } },
                { kind: "Field", name: { kind: "Name", value: "message" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  CompanyUserDeactivateMutation,
  CompanyUserDeactivateMutationVariables
>;
export const CompanyUserActivateDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "CompanyUserActivate" },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "companyUserActivate" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
                { kind: "Field", name: { kind: "Name", value: "email" } },
                { kind: "Field", name: { kind: "Name", value: "status" } },
                { kind: "Field", name: { kind: "Name", value: "roles" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  CompanyUserActivateMutation,
  CompanyUserActivateMutationVariables
>;
export const CreateCompanyBasicDetailsDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "CreateCompanyBasicDetails" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "input" },
          },
          type: {
            kind: "NonNullType",
            type: {
              kind: "NamedType",
              name: { kind: "Name", value: "CompanyCreateBasicDetailsInput" },
            },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "companyCreateBasicDetails" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "input" },
                value: {
                  kind: "Variable",
                  name: { kind: "Name", value: "input" },
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "basicDetails" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "name" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "address" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "phoneNumber" },
                      },
                      { kind: "Field", name: { kind: "Name", value: "email" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "website" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "industry" },
                      },
                      { kind: "Field", name: { kind: "Name", value: "size" } },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  CreateCompanyBasicDetailsMutation,
  CreateCompanyBasicDetailsMutationVariables
>;
export const UpdateCompanyBasicDetailsDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "UpdateCompanyBasicDetails" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "input" },
          },
          type: {
            kind: "NonNullType",
            type: {
              kind: "NamedType",
              name: { kind: "Name", value: "CompanyUpdateBasicDetailsInput" },
            },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "companyUpdateBasicDetails" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "input" },
                value: {
                  kind: "Variable",
                  name: { kind: "Name", value: "input" },
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "basicDetails" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "name" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "address" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "phoneNumber" },
                      },
                      { kind: "Field", name: { kind: "Name", value: "email" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "website" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "industry" },
                      },
                      { kind: "Field", name: { kind: "Name", value: "size" } },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  UpdateCompanyBasicDetailsMutation,
  UpdateCompanyBasicDetailsMutationVariables
>;
export const CustomerUpsertBasicDetailsDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "CustomerUpsertBasicDetails" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "input" },
          },
          type: {
            kind: "NonNullType",
            type: {
              kind: "NamedType",
              name: { kind: "Name", value: "CustomerUpsertBasicDetailsInput" },
            },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "customerUpsertBasicDetails" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "input" },
                value: {
                  kind: "Variable",
                  name: { kind: "Name", value: "input" },
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  CustomerUpsertBasicDetailsMutation,
  CustomerUpsertBasicDetailsMutationVariables
>;
export const CustomerUpsertAdditionalDetailsDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "CustomerUpsertAdditionalDetails" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "input" },
          },
          type: {
            kind: "NonNullType",
            type: {
              kind: "NamedType",
              name: {
                kind: "Name",
                value: "CustomerUpsertAdditionalDetailsInput",
              },
            },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "customerUpsertAdditionalDetails" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "input" },
                value: {
                  kind: "Variable",
                  name: { kind: "Name", value: "input" },
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  CustomerUpsertAdditionalDetailsMutation,
  CustomerUpsertAdditionalDetailsMutationVariables
>;
export const MasterProductUpsertDetailsDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "MasterProductUpsertDetails" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "input" },
          },
          type: {
            kind: "NonNullType",
            type: {
              kind: "NamedType",
              name: { kind: "Name", value: "MasterProductUpsertInput" },
            },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "masterProductUpsertDetails" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "input" },
                value: {
                  kind: "Variable",
                  name: { kind: "Name", value: "input" },
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "pricing" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  MasterProductUpsertDetailsMutation,
  MasterProductUpsertDetailsMutationVariables
>;
export const MasterProductUpsertAdditionalDetailsDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "MasterProductUpsertAdditionalDetails" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "input" },
          },
          type: {
            kind: "NonNullType",
            type: {
              kind: "NamedType",
              name: {
                kind: "Name",
                value: "MasterProductUpsertAdditionalDetailsInput",
              },
            },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: {
              kind: "Name",
              value: "masterProductUpsertAdditionalDetails",
            },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "input" },
                value: {
                  kind: "Variable",
                  name: { kind: "Name", value: "input" },
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "pricing" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  MasterProductUpsertAdditionalDetailsMutation,
  MasterProductUpsertAdditionalDetailsMutationVariables
>;
export const ProposalProductUpsertDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "mutation",
      name: { kind: "Name", value: "ProposalProductUpsert" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "input" },
          },
          type: {
            kind: "NonNullType",
            type: {
              kind: "NamedType",
              name: { kind: "Name", value: "QuoteUpsertInput" },
            },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "quoteUpsert" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "input" },
                value: {
                  kind: "Variable",
                  name: { kind: "Name", value: "input" },
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "status" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  ProposalProductUpsertMutation,
  ProposalProductUpsertMutationVariables
>;
export const OkDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "Ok" },
      selectionSet: {
        kind: "SelectionSet",
        selections: [{ kind: "Field", name: { kind: "Name", value: "ok" } }],
      },
    },
  ],
} as unknown as DocumentNode<OkQuery, OkQueryVariables>;
export const GetCompanyDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "GetCompany" },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "getCompany" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
                { kind: "Field", name: { kind: "Name", value: "status" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "onboarding" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "pendingSteps" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "stepType" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "mandatory" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "completed" },
                            },
                          ],
                        },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "completedSteps" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "stepType" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "mandatory" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "completed" },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<GetCompanyQuery, GetCompanyQueryVariables>;
export const GetCompanyBasicDetailsDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "GetCompanyBasicDetails" },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "getCompany" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "basicDetails" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "name" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "address" },
                      },
                      { kind: "Field", name: { kind: "Name", value: "email" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "website" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "phoneNumber" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "industry" },
                      },
                      { kind: "Field", name: { kind: "Name", value: "size" } },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  GetCompanyBasicDetailsQuery,
  GetCompanyBasicDetailsQueryVariables
>;
export const GetCompanyUsersDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "GetCompanyUsers" },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "getCompany" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "users" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      { kind: "Field", name: { kind: "Name", value: "name" } },
                      { kind: "Field", name: { kind: "Name", value: "email" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "status" },
                      },
                      { kind: "Field", name: { kind: "Name", value: "roles" } },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  GetCompanyUsersQuery,
  GetCompanyUsersQueryVariables
>;
export const GetCompanyUserDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "GetCompanyUser" },
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "getCompanyUser" },
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
                { kind: "Field", name: { kind: "Name", value: "email" } },
                { kind: "Field", name: { kind: "Name", value: "status" } },
                { kind: "Field", name: { kind: "Name", value: "roles" } },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<GetCompanyUserQuery, GetCompanyUserQueryVariables>;
export const GetAllCustomersDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "GetAllCustomers" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "status" },
          },
          type: {
            kind: "NamedType",
            name: { kind: "Name", value: "CustomerStatus" },
          },
        },
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "stage" },
          },
          type: {
            kind: "NamedType",
            name: { kind: "Name", value: "CustomerStage" },
          },
        },
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "customerType" },
          },
          type: {
            kind: "NamedType",
            name: { kind: "Name", value: "CustomerType" },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "getCustomers" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "filters" },
                value: {
                  kind: "ObjectValue",
                  fields: [
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "status" },
                      value: {
                        kind: "Variable",
                        name: { kind: "Name", value: "status" },
                      },
                    },
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "stage" },
                      value: {
                        kind: "Variable",
                        name: { kind: "Name", value: "stage" },
                      },
                    },
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "customerType" },
                      value: {
                        kind: "Variable",
                        name: { kind: "Name", value: "customerType" },
                      },
                    },
                  ],
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "company" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                    ],
                  },
                },
                { kind: "Field", name: { kind: "Name", value: "status" } },
                { kind: "Field", name: { kind: "Name", value: "stage" } },
                { kind: "Field", name: { kind: "Name", value: "type" } },
                {
                  kind: "InlineFragment",
                  typeCondition: {
                    kind: "NamedType",
                    name: { kind: "Name", value: "CustomerIndividual" },
                  },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "basicDetails" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "contactDetails" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [
                                  {
                                    kind: "Field",
                                    name: {
                                      kind: "Name",
                                      value: "contactType",
                                    },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "name" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "title" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "email" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "phoneNo" },
                                  },
                                ],
                              },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "referralSource" },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
                {
                  kind: "InlineFragment",
                  typeCondition: {
                    kind: "NamedType",
                    name: { kind: "Name", value: "CustomerBusiness" },
                  },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "basicDetails" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "legalName" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "website" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "size" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "industry" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "referralSource" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "contactDetails" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [
                                  {
                                    kind: "Field",
                                    name: {
                                      kind: "Name",
                                      value: "contactType",
                                    },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "name" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "title" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "email" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "phoneNo" },
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  GetAllCustomersQuery,
  GetAllCustomersQueryVariables
>;
export const GetCustomerByIdDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "GetCustomerById" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "id" } },
          type: {
            kind: "NonNullType",
            type: { kind: "NamedType", name: { kind: "Name", value: "ID" } },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "getCustomer" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "id" },
                value: {
                  kind: "Variable",
                  name: { kind: "Name", value: "id" },
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "status" } },
                { kind: "Field", name: { kind: "Name", value: "stage" } },
                { kind: "Field", name: { kind: "Name", value: "type" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "company" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                    ],
                  },
                },
                {
                  kind: "InlineFragment",
                  typeCondition: {
                    kind: "NamedType",
                    name: { kind: "Name", value: "CustomerIndividual" },
                  },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "basicDetails" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "contactDetails" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [
                                  {
                                    kind: "Field",
                                    name: {
                                      kind: "Name",
                                      value: "contactType",
                                    },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "name" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "title" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "email" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "phoneNo" },
                                  },
                                ],
                              },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "referralSource" },
                            },
                          ],
                        },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "notes" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "id" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "content" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "tags" },
                            },
                          ],
                        },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "assignments" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "accountManager" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "id" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "name" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "email" },
                                  },
                                ],
                              },
                            },
                            {
                              kind: "Field",
                              name: {
                                kind: "Name",
                                value: "supportRepresentative",
                              },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "id" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "name" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "email" },
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "customTags" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "id" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "key" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "label" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "value" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "type" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "description" },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
                {
                  kind: "InlineFragment",
                  typeCondition: {
                    kind: "NamedType",
                    name: { kind: "Name", value: "CustomerBusiness" },
                  },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "basicDetails" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "legalName" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "website" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "size" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "industry" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "referralSource" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "contactDetails" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [
                                  {
                                    kind: "Field",
                                    name: {
                                      kind: "Name",
                                      value: "contactType",
                                    },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "name" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "title" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "email" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "phoneNo" },
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "notes" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "id" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "content" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "tags" },
                            },
                          ],
                        },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "assignments" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "accountManager" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "id" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "name" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "email" },
                                  },
                                ],
                              },
                            },
                            {
                              kind: "Field",
                              name: {
                                kind: "Name",
                                value: "supportRepresentative",
                              },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "id" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "name" },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "email" },
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "customTags" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "id" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "key" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "label" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "value" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "type" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "description" },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  GetCustomerByIdQuery,
  GetCustomerByIdQueryVariables
>;
export const GetAllProductsDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "GetAllProducts" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: {
            kind: "Variable",
            name: { kind: "Name", value: "status" },
          },
          type: {
            kind: "NamedType",
            name: { kind: "Name", value: "ProductStatus" },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "masterProductsGet" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "filters" },
                value: {
                  kind: "ObjectValue",
                  fields: [
                    {
                      kind: "ObjectField",
                      name: { kind: "Name", value: "status" },
                      value: {
                        kind: "Variable",
                        name: { kind: "Name", value: "status" },
                      },
                    },
                  ],
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
                { kind: "Field", name: { kind: "Name", value: "description" } },
                { kind: "Field", name: { kind: "Name", value: "status" } },
                { kind: "Field", name: { kind: "Name", value: "productCode" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "dimensions" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "key" } },
                      { kind: "Field", name: { kind: "Name", value: "value" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "pricing" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "chargePolicy" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "costPrice" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "value" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "currency" },
                            },
                          ],
                        },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "listPrice" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "value" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "currency" },
                            },
                          ],
                        },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "sellingPrice" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "value" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "currency" },
                            },
                          ],
                        },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "unit" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "unit" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "unitType" },
                            },
                          ],
                        },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "discount" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "id" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "discountType" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "discountValue" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "value" },
                                    selectionSet: {
                                      kind: "SelectionSet",
                                      selections: [
                                        {
                                          kind: "Field",
                                          name: {
                                            kind: "Name",
                                            value: "value",
                                          },
                                        },
                                        {
                                          kind: "Field",
                                          name: {
                                            kind: "Name",
                                            value: "currency",
                                          },
                                        },
                                      ],
                                    },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "percentage" },
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<GetAllProductsQuery, GetAllProductsQueryVariables>;
export const GetProductByIdDocument = {
  kind: "Document",
  definitions: [
    {
      kind: "OperationDefinition",
      operation: "query",
      name: { kind: "Name", value: "GetProductById" },
      variableDefinitions: [
        {
          kind: "VariableDefinition",
          variable: { kind: "Variable", name: { kind: "Name", value: "id" } },
          type: {
            kind: "NonNullType",
            type: { kind: "NamedType", name: { kind: "Name", value: "ID" } },
          },
        },
      ],
      selectionSet: {
        kind: "SelectionSet",
        selections: [
          {
            kind: "Field",
            name: { kind: "Name", value: "masterProductGetById" },
            arguments: [
              {
                kind: "Argument",
                name: { kind: "Name", value: "id" },
                value: {
                  kind: "Variable",
                  name: { kind: "Name", value: "id" },
                },
              },
            ],
            selectionSet: {
              kind: "SelectionSet",
              selections: [
                { kind: "Field", name: { kind: "Name", value: "id" } },
                { kind: "Field", name: { kind: "Name", value: "name" } },
                { kind: "Field", name: { kind: "Name", value: "description" } },
                { kind: "Field", name: { kind: "Name", value: "status" } },
                { kind: "Field", name: { kind: "Name", value: "productCode" } },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "dimensions" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "key" } },
                      { kind: "Field", name: { kind: "Name", value: "value" } },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "documents" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "file" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "signedReadURL" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "signedWriteURL" },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "customTags" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      { kind: "Field", name: { kind: "Name", value: "key" } },
                      { kind: "Field", name: { kind: "Name", value: "label" } },
                      { kind: "Field", name: { kind: "Name", value: "value" } },
                      { kind: "Field", name: { kind: "Name", value: "type" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "description" },
                      },
                    ],
                  },
                },
                {
                  kind: "Field",
                  name: { kind: "Name", value: "pricing" },
                  selectionSet: {
                    kind: "SelectionSet",
                    selections: [
                      { kind: "Field", name: { kind: "Name", value: "id" } },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "chargePolicy" },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "costPrice" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "value" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "currency" },
                            },
                          ],
                        },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "listPrice" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "value" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "currency" },
                            },
                          ],
                        },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "sellingPrice" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "value" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "currency" },
                            },
                          ],
                        },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "unit" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "unit" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "unitType" },
                            },
                          ],
                        },
                      },
                      {
                        kind: "Field",
                        name: { kind: "Name", value: "discount" },
                        selectionSet: {
                          kind: "SelectionSet",
                          selections: [
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "id" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "discountType" },
                            },
                            {
                              kind: "Field",
                              name: { kind: "Name", value: "discountValue" },
                              selectionSet: {
                                kind: "SelectionSet",
                                selections: [
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "value" },
                                    selectionSet: {
                                      kind: "SelectionSet",
                                      selections: [
                                        {
                                          kind: "Field",
                                          name: {
                                            kind: "Name",
                                            value: "value",
                                          },
                                        },
                                        {
                                          kind: "Field",
                                          name: {
                                            kind: "Name",
                                            value: "currency",
                                          },
                                        },
                                      ],
                                    },
                                  },
                                  {
                                    kind: "Field",
                                    name: { kind: "Name", value: "percentage" },
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<GetProductByIdQuery, GetProductByIdQueryVariables>;
